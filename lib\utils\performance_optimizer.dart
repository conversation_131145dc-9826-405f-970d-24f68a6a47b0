import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'responsive_helper.dart';

class PerformanceOptimizer {
  static bool _isInitialized = false;
  static DevicePerformance? _cachedPerformance;

  // Initialize performance optimizations
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Set preferred orientations for mobile
      if (Platform.isAndroid || Platform.isIOS) {
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]);
      }

      // Optimize system UI
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );

      // Enable hardware acceleration
      if (Platform.isAndroid) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      }

      _isInitialized = true;
    } catch (e) {
      debugPrint('Performance optimization initialization failed: $e');
    }
  }

  // Get cached device performance
  static DevicePerformance getDevicePerformance(BuildContext context) {
    _cachedPerformance ??= ResponsiveHelper.getDevicePerformance(context);
    return _cachedPerformance!;
  }

  // Adaptive image loading based on device performance
  static Widget buildOptimizedImage({
    required String imageUrl,
    required double width,
    required double height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    required BuildContext context,
  }) {
    final performance = getDevicePerformance(context);
    final quality = _getImageQuality(performance);
    final cacheWidth = _getCacheWidth(width, performance);
    final cacheHeight = _getCacheHeight(height, performance);

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: Image.network(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: cacheWidth,
        cacheHeight: cacheHeight,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          
          return Container(
            width: width,
            height: height,
            color: Colors.grey[200],
            child: Center(
              child: performance == DevicePerformance.low
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: width,
            height: height,
            color: Colors.grey[300],
            child: Icon(
              Icons.broken_image,
              color: Colors.grey[500],
              size: performance == DevicePerformance.low ? 24 : 32,
            ),
          );
        },
      ),
    );
  }

  // Adaptive list view for better performance
  static Widget buildOptimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    required BuildContext context,
    ScrollController? controller,
    EdgeInsets? padding,
  }) {
    final performance = getDevicePerformance(context);
    final maxItems = _getMaxVisibleItems(performance);
    
    if (performance == DevicePerformance.low && itemCount > maxItems) {
      // Use lazy loading for low-performance devices
      return ListView.builder(
        controller: controller,
        padding: padding,
        itemCount: itemCount,
        itemBuilder: itemBuilder,
        cacheExtent: 200, // Reduced cache for low-end devices
        addAutomaticKeepAlives: false,
        addRepaintBoundaries: false,
      );
    } else {
      // Standard list view for better devices
      return ListView.builder(
        controller: controller,
        padding: padding,
        itemCount: itemCount,
        itemBuilder: itemBuilder,
        cacheExtent: performance == DevicePerformance.high ? 500 : 300,
      );
    }
  }

  // Adaptive animation wrapper
  static Widget buildOptimizedAnimation({
    required Widget child,
    required BuildContext context,
    Duration? duration,
    Curve curve = Curves.easeInOut,
  }) {
    final performance = getDevicePerformance(context);
    
    if (performance == DevicePerformance.low) {
      // Disable animations on low-end devices
      return child;
    }
    
    final animationDuration = duration ?? _getAnimationDuration(performance);
    
    return AnimatedContainer(
      duration: animationDuration,
      curve: curve,
      child: child,
    );
  }

  // Memory-efficient grid view
  static Widget buildOptimizedGridView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    required BuildContext context,
    int? crossAxisCount,
    double? childAspectRatio,
    ScrollController? controller,
    EdgeInsets? padding,
  }) {
    final performance = getDevicePerformance(context);
    final columns = crossAxisCount ?? ResponsiveHelper.getGridColumns(context);
    
    return GridView.builder(
      controller: controller,
      padding: padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        childAspectRatio: childAspectRatio ?? 0.8,
        crossAxisSpacing: performance == DevicePerformance.low ? 8 : 12,
        mainAxisSpacing: performance == DevicePerformance.low ? 8 : 12,
      ),
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      cacheExtent: performance == DevicePerformance.low ? 200 : 400,
      addAutomaticKeepAlives: performance != DevicePerformance.low,
    );
  }

  // Optimized card widget
  static Widget buildOptimizedCard({
    required Widget child,
    required BuildContext context,
    EdgeInsets? margin,
    EdgeInsets? padding,
    double? elevation,
  }) {
    final performance = getDevicePerformance(context);
    
    return Card(
      margin: margin ?? const EdgeInsets.all(8),
      elevation: performance == DevicePerformance.low ? 1 : (elevation ?? 2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          performance == DevicePerformance.low ? 8 : 12,
        ),
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(12),
        child: child,
      ),
    );
  }

  // Helper methods
  static int _getImageQuality(DevicePerformance performance) {
    switch (performance) {
      case DevicePerformance.high:
        return 90;
      case DevicePerformance.medium:
        return 75;
      case DevicePerformance.low:
        return 60;
    }
  }

  static int? _getCacheWidth(double width, DevicePerformance performance) {
    switch (performance) {
      case DevicePerformance.high:
        return (width * 2).toInt();
      case DevicePerformance.medium:
        return (width * 1.5).toInt();
      case DevicePerformance.low:
        return width.toInt();
    }
  }

  static int? _getCacheHeight(double height, DevicePerformance performance) {
    switch (performance) {
      case DevicePerformance.high:
        return (height * 2).toInt();
      case DevicePerformance.medium:
        return (height * 1.5).toInt();
      case DevicePerformance.low:
        return height.toInt();
    }
  }

  static int _getMaxVisibleItems(DevicePerformance performance) {
    switch (performance) {
      case DevicePerformance.high:
        return 100;
      case DevicePerformance.medium:
        return 50;
      case DevicePerformance.low:
        return 25;
    }
  }

  static Duration _getAnimationDuration(DevicePerformance performance) {
    switch (performance) {
      case DevicePerformance.high:
        return const Duration(milliseconds: 300);
      case DevicePerformance.medium:
        return const Duration(milliseconds: 250);
      case DevicePerformance.low:
        return const Duration(milliseconds: 150);
    }
  }

  // Memory management
  static void clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  // Force garbage collection (use sparingly)
  static void forceGarbageCollection() {
    // This is a hint to the system, not guaranteed
    SystemChannels.platform.invokeMethod('SystemChrome.restoreSystemUIOverlays');
  }

  // Check available memory (Android specific)
  static Future<bool> hasLowMemory() async {
    if (!Platform.isAndroid) return false;
    
    try {
      // This is a simplified check - in a real app you might use
      // platform channels to get actual memory info
      return false;
    } catch (e) {
      return false;
    }
  }
}
