import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import 'signin_screen.dart';
import 'root_app.dart';
import '../theme/color.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AuthService authService = AuthService();

    return StreamBuilder<User?>(
      stream: authService.authStateChanges,
      builder: (context, snapshot) {
        print("AuthWrapper - Connection state: ${snapshot.connectionState}");
        print("AuthWrapper - Has data: ${snapshot.hasData}");
        print("AuthWrapper - Data: ${snapshot.data}");
        print("AuthWrapper - Error: ${snapshot.error}");

        // Add timeout handling for debug builds
        if (snapshot.connectionState == ConnectionState.none) {
          print("AuthWrapper - No connection, showing SignIn");
          return const SignInScreen();
        }
        
        // Show loading indicator while checking auth state
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColor.primary,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Loading...',
                    style: TextStyle(
                      color: AppColor.textColor,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        
        // Handle errors
        if (snapshot.hasError) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 64,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Error: ${snapshot.error}',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      // Force rebuild
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(builder: (context) => const AuthWrapper()),
                      );
                    },
                    child: Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }
        
        // If user is logged in (including anonymous), show the main app
        if (snapshot.hasData && snapshot.data != null) {
          User user = snapshot.data!;
          if (user.isAnonymous) {
            print("Anonymous user logged in");
          } else {
            print("User is logged in: ${user.email}");
          }
          return const RootApp();
        }

        // If user is not logged in, try anonymous sign-in first
        print("User is not logged in, attempting anonymous sign-in");
        return FutureBuilder<UserCredential?>(
          future: authService.signInAnonymously(),
          builder: (context, anonymousSnapshot) {
            if (anonymousSnapshot.connectionState == ConnectionState.waiting) {
              return Scaffold(
                backgroundColor: Colors.white,
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: AppColor.primary,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'Setting up your session...',
                        style: TextStyle(
                          color: AppColor.textColor,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            if (anonymousSnapshot.hasError) {
              print("Anonymous sign-in failed: ${anonymousSnapshot.error}");
              // If anonymous sign-in fails, show sign-in screen
              return const SignInScreen();
            }

            // Anonymous sign-in successful, show main app
            if (anonymousSnapshot.hasData) {
              print("Anonymous sign-in successful");
              return const RootApp();
            }

            // Fallback to sign-in screen
            return const SignInScreen();
          },
        );
      },
    );
  }
}