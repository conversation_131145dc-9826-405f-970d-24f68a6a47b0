import 'package:flutter/material.dart';
import 'dart:io';

class ResponsiveHelper {
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  // Screen size categories
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  // Device type detection
  static bool isAndroid() => Platform.isAndroid;
  static bool isIOS() => Platform.isIOS;

  // Screen dimensions
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  // Safe area dimensions
  static double safeAreaHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height - 
           mediaQuery.padding.top - 
           mediaQuery.padding.bottom;
  }

  static double safeAreaWidth(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.width - 
           mediaQuery.padding.left - 
           mediaQuery.padding.right;
  }

  // Responsive values
  static double responsiveValue(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    if (isDesktop(context)) {
      return desktop ?? tablet ?? mobile;
    } else if (isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }

  // Grid columns based on screen size
  static int getGridColumns(BuildContext context) {
    if (isDesktop(context)) return 3;
    if (isTablet(context)) return 2;
    return 1;
  }

  // Padding based on screen size
  static EdgeInsets getScreenPadding(BuildContext context) {
    return EdgeInsets.symmetric(
      horizontal: responsiveValue(
        context,
        mobile: 16.0,
        tablet: 24.0,
        desktop: 32.0,
      ),
      vertical: responsiveValue(
        context,
        mobile: 8.0,
        tablet: 12.0,
        desktop: 16.0,
      ),
    );
  }

  // Font sizes based on screen size
  static double getHeadingFontSize(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 24.0,
      tablet: 28.0,
      desktop: 32.0,
    );
  }

  static double getBodyFontSize(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 14.0,
      tablet: 16.0,
      desktop: 16.0,
    );
  }

  static double getCaptionFontSize(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 12.0,
      tablet: 13.0,
      desktop: 14.0,
    );
  }

  // Card dimensions
  static double getCardHeight(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 280.0,
      tablet: 320.0,
      desktop: 350.0,
    );
  }

  static double getImageHeight(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 200.0,
      tablet: 240.0,
      desktop: 280.0,
    );
  }

  // Button dimensions
  static double getButtonHeight(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 48.0,
      tablet: 52.0,
      desktop: 56.0,
    );
  }

  // Icon sizes
  static double getIconSize(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 24.0,
      tablet: 28.0,
      desktop: 32.0,
    );
  }

  // App bar height
  static double getAppBarHeight(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 56.0,
      tablet: 64.0,
      desktop: 72.0,
    );
  }

  // Device performance category
  static DevicePerformance getDevicePerformance(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final pixelRatio = mediaQuery.devicePixelRatio;
    final screenSize = mediaQuery.size;
    
    // Simple heuristic based on screen size and pixel ratio
    final totalPixels = screenSize.width * screenSize.height * pixelRatio;
    
    if (totalPixels > 2000000) {
      return DevicePerformance.high;
    } else if (totalPixels > 1000000) {
      return DevicePerformance.medium;
    } else {
      return DevicePerformance.low;
    }
  }

  // Adaptive image quality based on device performance
  static int getImageQuality(BuildContext context) {
    switch (getDevicePerformance(context)) {
      case DevicePerformance.high:
        return 90;
      case DevicePerformance.medium:
        return 75;
      case DevicePerformance.low:
        return 60;
    }
  }

  // Adaptive animation duration
  static Duration getAnimationDuration(BuildContext context) {
    switch (getDevicePerformance(context)) {
      case DevicePerformance.high:
        return const Duration(milliseconds: 300);
      case DevicePerformance.medium:
        return const Duration(milliseconds: 250);
      case DevicePerformance.low:
        return const Duration(milliseconds: 200);
    }
  }

  // Check if device supports advanced features
  static bool supportsAdvancedFeatures(BuildContext context) {
    return getDevicePerformance(context) != DevicePerformance.low;
  }

  // Adaptive list item count for performance
  static int getMaxListItems(BuildContext context) {
    switch (getDevicePerformance(context)) {
      case DevicePerformance.high:
        return 50;
      case DevicePerformance.medium:
        return 30;
      case DevicePerformance.low:
        return 20;
    }
  }

  // Check if device has notch or cutout
  static bool hasNotch(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.padding.top > 24; // Standard status bar height
  }

  // Get safe area insets
  static EdgeInsets getSafeAreaInsets(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  // Orientation helpers
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }
}

enum DevicePerformance {
  low,
  medium,
  high,
}

// Extension for easier usage
extension ResponsiveContext on BuildContext {
  bool get isMobile => ResponsiveHelper.isMobile(this);
  bool get isTablet => ResponsiveHelper.isTablet(this);
  bool get isDesktop => ResponsiveHelper.isDesktop(this);
  
  double get screenWidth => ResponsiveHelper.screenWidth(this);
  double get screenHeight => ResponsiveHelper.screenHeight(this);
  
  EdgeInsets get screenPadding => ResponsiveHelper.getScreenPadding(this);
  
  DevicePerformance get devicePerformance => ResponsiveHelper.getDevicePerformance(this);
  
  bool get supportsAdvancedFeatures => ResponsiveHelper.supportsAdvancedFeatures(this);
}
