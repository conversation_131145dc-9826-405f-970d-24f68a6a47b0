# App Icon Assets

## Required App Icon File

Please add your app icon as `appicon.png` in this directory.

### App Icon Specifications:
- **File Name**: `appicon.png`
- **Format**: PNG with transparent background
- **Recommended Size**: 1024x1024 pixels (minimum 512x512)
- **Aspect Ratio**: 1:1 (perfect square)
- **Background**: Transparent or solid color
- **Style**: Hotel/travel themed, professional design

### Hotel Booking App Icon Ideas:
1. **Hotel Building + Mobile**: Stylized hotel with phone/app icon
2. **Bed + Location Pin**: Hotel bed with location marker
3. **Key + Touch**: Hotel key with touch gesture
4. **H + Booking**: Letter "H" with booking/calendar element
5. **Building + Search**: Hotel building with magnifying glass

### Color Scheme Suggestions:
- **Primary**: Blue (#2196F3) - Trust and reliability
- **Secondary**: Orange (#FF9800) - Warmth and hospitality
- **Accent**: Green (#4CAF50) - Success and confirmation
- **Background**: White or transparent

### Usage:
This icon will be used for:
- App launcher icon (Android/iOS)
- App store listings
- Splash screen
- About page
- Email templates

### File Location:
```
assets/
└── icons/
    └── appicon.png  ← Add your app icon here
```

### After adding the icon:
1. Make sure the file is named exactly `appicon.png`
2. Run `flutter pub get` to refresh assets
3. Run `flutter pub run flutter_launcher_icons:main` to generate all icon sizes
4. Test the icon on different devices and screen densities

### Current Status:
❌ App icon missing - Please add `appicon.png` to this directory

### Icon Generation:
Once you add `appicon.png`, the app will automatically generate:
- Android: mipmap-hdpi, mipmap-mdpi, mipmap-xhdpi, mipmap-xxhdpi, mipmap-xxxhdpi
- iOS: <EMAIL>, <EMAIL>, etc.
- Web: favicon.ico, icons-192.png, icons-512.png
