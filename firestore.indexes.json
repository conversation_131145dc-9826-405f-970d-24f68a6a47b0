{"indexes": [{"collectionGroup": "hotels", "queryScope": "COLLECTION", "fields": [{"fieldPath": "city", "order": "ASCENDING"}, {"fieldPath": "isAvailable", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "hotels", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isAvailable", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "hotels", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isAvailable", "order": "ASCENDING"}, {"fieldPath": "pricePerNight", "order": "ASCENDING"}]}, {"collectionGroup": "rooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hotelId", "order": "ASCENDING"}, {"fieldPath": "isAvailable", "order": "ASCENDING"}, {"fieldPath": "pricePerNight", "order": "ASCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "roomId", "order": "ASCENDING"}, {"fieldPath": "status", "arrayConfig": "CONTAINS"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hotelId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}