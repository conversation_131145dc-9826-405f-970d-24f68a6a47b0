import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/review.dart';

class ReviewService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get reviews for a hotel
  Stream<List<Review>> getHotelReviews(String hotelId) {
    return _firestore
        .collection('reviews')
        .where('hotelId', isEqualTo: hotelId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => Review.fromFirestore(doc)).toList());
  }

  // Get reviews by user
  Stream<List<Review>> getUserReviews(String userId) {
    return _firestore
        .collection('reviews')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => Review.fromFirestore(doc)).toList());
  }

  // Add a new review
  Future<String> addReview({
    required String hotelId,
    required String bookingId,
    required double rating,
    required String title,
    required String comment,
    List<String> images = const [],
  }) async {
    try {
      User? currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw 'User must be logged in to submit a review';
      }

      // Get user data
      DocumentSnapshot userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      Map<String, dynamic>? userData = userDoc.data() as Map<String, dynamic>?;

      // Check if user has already reviewed this hotel
      QuerySnapshot existingReview = await _firestore
          .collection('reviews')
          .where('userId', isEqualTo: currentUser.uid)
          .where('hotelId', isEqualTo: hotelId)
          .limit(1)
          .get();

      if (existingReview.docs.isNotEmpty) {
        throw 'You have already reviewed this hotel';
      }

      // Create review
      Review review = Review(
        id: '',
        userId: currentUser.uid,
        userName: userData?['fullName'] ?? currentUser.displayName ?? 'Anonymous',
        userProfileImage: userData?['profileImageUrl'] ?? '',
        hotelId: hotelId,
        bookingId: bookingId,
        rating: rating,
        title: title,
        comment: comment,
        images: images,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isVerified: false, // Will be verified by admin
        helpfulVotes: {},
      );

      // Add to Firestore
      DocumentReference docRef = await _firestore.collection('reviews').add(review.toFirestore());

      // Update hotel rating
      await _updateHotelRating(hotelId);

      return docRef.id;
    } catch (e) {
      throw 'Error adding review: $e';
    }
  }

  // Update a review
  Future<void> updateReview({
    required String reviewId,
    required double rating,
    required String title,
    required String comment,
    List<String>? images,
  }) async {
    try {
      User? currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw 'User must be logged in to update a review';
      }

      // Check if review exists and belongs to user
      DocumentSnapshot reviewDoc = await _firestore.collection('reviews').doc(reviewId).get();
      if (!reviewDoc.exists) {
        throw 'Review not found';
      }

      Review review = Review.fromFirestore(reviewDoc);
      if (review.userId != currentUser.uid) {
        throw 'You can only update your own reviews';
      }

      // Update review
      Map<String, dynamic> updateData = {
        'rating': rating,
        'title': title,
        'comment': comment,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      };

      if (images != null) {
        updateData['images'] = images;
      }

      await _firestore.collection('reviews').doc(reviewId).update(updateData);

      // Update hotel rating
      await _updateHotelRating(review.hotelId);
    } catch (e) {
      throw 'Error updating review: $e';
    }
  }

  // Delete a review
  Future<void> deleteReview(String reviewId) async {
    try {
      User? currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw 'User must be logged in to delete a review';
      }

      // Check if review exists and belongs to user
      DocumentSnapshot reviewDoc = await _firestore.collection('reviews').doc(reviewId).get();
      if (!reviewDoc.exists) {
        throw 'Review not found';
      }

      Review review = Review.fromFirestore(reviewDoc);
      if (review.userId != currentUser.uid) {
        throw 'You can only delete your own reviews';
      }

      // Delete review
      await _firestore.collection('reviews').doc(reviewId).delete();

      // Update hotel rating
      await _updateHotelRating(review.hotelId);
    } catch (e) {
      throw 'Error deleting review: $e';
    }
  }

  // Vote on review helpfulness
  Future<void> voteOnReview(String reviewId, bool isHelpful) async {
    try {
      User? currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw 'User must be logged in to vote on reviews';
      }

      DocumentReference reviewRef = _firestore.collection('reviews').doc(reviewId);
      
      await _firestore.runTransaction((transaction) async {
        DocumentSnapshot reviewDoc = await transaction.get(reviewRef);
        if (!reviewDoc.exists) {
          throw 'Review not found';
        }

        Review review = Review.fromFirestore(reviewDoc);
        Map<String, int> newHelpfulVotes = Map.from(review.helpfulVotes);
        
        // Update or add vote
        newHelpfulVotes[currentUser.uid] = isHelpful ? 1 : -1;

        transaction.update(reviewRef, {
          'helpfulVotes': newHelpfulVotes,
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });
      });
    } catch (e) {
      throw 'Error voting on review: $e';
    }
  }

  // Get review summary for a hotel
  Future<ReviewSummary> getReviewSummary(String hotelId) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('reviews')
          .where('hotelId', isEqualTo: hotelId)
          .get();

      List<Review> reviews = snapshot.docs.map((doc) => Review.fromFirestore(doc)).toList();
      return ReviewSummary.fromReviews(reviews);
    } catch (e) {
      throw 'Error getting review summary: $e';
    }
  }

  // Update hotel rating based on reviews
  Future<void> _updateHotelRating(String hotelId) async {
    try {
      QuerySnapshot reviewsSnapshot = await _firestore
          .collection('reviews')
          .where('hotelId', isEqualTo: hotelId)
          .get();

      if (reviewsSnapshot.docs.isEmpty) {
        // No reviews, set default rating
        await _firestore.collection('hotels').doc(hotelId).update({
          'rating': 0.0,
          'reviewCount': 0,
        });
        return;
      }

      List<Review> reviews = reviewsSnapshot.docs.map((doc) => Review.fromFirestore(doc)).toList();
      double totalRating = reviews.fold(0.0, (sum, review) => sum + review.rating);
      double averageRating = totalRating / reviews.length;

      await _firestore.collection('hotels').doc(hotelId).update({
        'rating': double.parse(averageRating.toStringAsFixed(1)),
        'reviewCount': reviews.length,
      });
    } catch (e) {
      print('Error updating hotel rating: $e');
    }
  }

  // Check if user can review hotel (has booking)
  Future<bool> canUserReviewHotel(String hotelId) async {
    try {
      User? currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      // Check if user has a completed booking for this hotel
      QuerySnapshot bookingSnapshot = await _firestore
          .collection('bookings')
          .where('userId', isEqualTo: currentUser.uid)
          .where('hotelId', isEqualTo: hotelId)
          .where('status', isEqualTo: 'completed')
          .limit(1)
          .get();

      if (bookingSnapshot.docs.isEmpty) return false;

      // Check if user has already reviewed this hotel
      QuerySnapshot reviewSnapshot = await _firestore
          .collection('reviews')
          .where('userId', isEqualTo: currentUser.uid)
          .where('hotelId', isEqualTo: hotelId)
          .limit(1)
          .get();

      return reviewSnapshot.docs.isEmpty;
    } catch (e) {
      return false;
    }
  }
}
