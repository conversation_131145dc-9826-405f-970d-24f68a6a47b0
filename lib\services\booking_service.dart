import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/booking.dart';
import '../models/hotel.dart';

class BookingService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Create a new booking
  Future<String> createBooking({
    required String hotelId,
    required String roomId,
    required String hotelName,
    required String roomName,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int guests,
    required double totalAmount,
    required String currency,
    Map<String, dynamic>? guestDetails,
    String? specialRequests,
  }) async {
    try {
      User? currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      Booking booking = Booking(
        id: '', // Will be set by Firestore
        userId: currentUser.uid,
        hotelId: hotelId,
        roomId: roomId,
        hotelName: hotelName,
        roomName: roomName,
        checkInDate: checkInDate,
        checkOutDate: checkOutDate,
        guests: guests,
        totalAmount: totalAmount,
        currency: currency,
        status: BookingStatus.pending,
        paymentStatus: PaymentStatus.pending,
        guestDetails: guestDetails,
        specialRequests: specialRequests,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      DocumentReference docRef = await _firestore
          .collection('bookings')
          .add(booking.toFirestore());

      return docRef.id;
    } catch (e) {
      throw 'Error creating booking: $e';
    }
  }

  // Get user's bookings
  Stream<List<Booking>> getUserBookings({String? userId}) {
    String uid = userId ?? _auth.currentUser?.uid ?? '';
    if (uid.isEmpty) {
      return Stream.value([]);
    }

    return _firestore
        .collection('bookings')
        .where('userId', isEqualTo: uid)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => Booking.fromFirestore(doc)).toList());
  }

  // Get booking by ID
  Future<Booking?> getBookingById(String bookingId) async {
    try {
      DocumentSnapshot doc = await _firestore.collection('bookings').doc(bookingId).get();
      if (doc.exists) {
        return Booking.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw 'Error fetching booking: $e';
    }
  }

  // Update booking status
  Future<void> updateBookingStatus({
    required String bookingId,
    required BookingStatus status,
  }) async {
    try {
      await _firestore.collection('bookings').doc(bookingId).update({
        'status': status.toString().split('.').last,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw 'Error updating booking status: $e';
    }
  }

  // Update payment status
  Future<void> updatePaymentStatus({
    required String bookingId,
    required PaymentStatus paymentStatus,
    String? paymentId,
  }) async {
    try {
      Map<String, dynamic> updateData = {
        'paymentStatus': paymentStatus.toString().split('.').last,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (paymentId != null) {
        updateData['paymentId'] = paymentId;
      }

      await _firestore.collection('bookings').doc(bookingId).update(updateData);

      // If payment is successful, update booking status to confirmed
      if (paymentStatus == PaymentStatus.paid) {
        await updateBookingStatus(bookingId: bookingId, status: BookingStatus.confirmed);
      }
    } catch (e) {
      throw 'Error updating payment status: $e';
    }
  }

  // Cancel booking
  Future<void> cancelBooking(String bookingId) async {
    try {
      Booking? booking = await getBookingById(bookingId);
      if (booking == null) {
        throw 'Booking not found';
      }

      if (!booking.canCancel) {
        throw 'Booking cannot be cancelled';
      }

      await updateBookingStatus(bookingId: bookingId, status: BookingStatus.cancelled);
    } catch (e) {
      throw 'Error cancelling booking: $e';
    }
  }

  // Check in
  Future<void> checkIn(String bookingId) async {
    try {
      Booking? booking = await getBookingById(bookingId);
      if (booking == null) {
        throw 'Booking not found';
      }

      if (!booking.canCheckIn) {
        throw 'Cannot check in at this time';
      }

      await updateBookingStatus(bookingId: bookingId, status: BookingStatus.checkedIn);
    } catch (e) {
      throw 'Error checking in: $e';
    }
  }

  // Check out
  Future<void> checkOut(String bookingId) async {
    try {
      Booking? booking = await getBookingById(bookingId);
      if (booking == null) {
        throw 'Booking not found';
      }

      if (!booking.canCheckOut) {
        throw 'Cannot check out at this time';
      }

      await updateBookingStatus(bookingId: bookingId, status: BookingStatus.checkedOut);
    } catch (e) {
      throw 'Error checking out: $e';
    }
  }

  // Calculate total amount
  double calculateTotalAmount({
    required double pricePerNight,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    double taxRate = 0.1, // 10% tax
    double serviceCharge = 0.05, // 5% service charge
  }) {
    int numberOfNights = checkOutDate.difference(checkInDate).inDays;
    if (numberOfNights <= 0) {
      throw 'Invalid date range';
    }

    double subtotal = pricePerNight * numberOfNights;
    double tax = subtotal * taxRate;
    double service = subtotal * serviceCharge;
    
    return subtotal + tax + service;
  }

  // Get booking statistics for user
  Future<Map<String, dynamic>> getBookingStatistics({String? userId}) async {
    try {
      String uid = userId ?? _auth.currentUser?.uid ?? '';
      if (uid.isEmpty) {
        return {
          'totalBookings': 0,
          'completedBookings': 0,
          'cancelledBookings': 0,
          'totalSpent': 0.0,
        };
      }

      QuerySnapshot snapshot = await _firestore
          .collection('bookings')
          .where('userId', isEqualTo: uid)
          .get();

      List<Booking> bookings = snapshot.docs.map((doc) => Booking.fromFirestore(doc)).toList();

      int totalBookings = bookings.length;
      int completedBookings = bookings.where((b) => b.status == BookingStatus.checkedOut).length;
      int cancelledBookings = bookings.where((b) => b.status == BookingStatus.cancelled).length;
      double totalSpent = bookings
          .where((b) => b.paymentStatus == PaymentStatus.paid)
          .fold(0.0, (sum, booking) => sum + booking.totalAmount);

      return {
        'totalBookings': totalBookings,
        'completedBookings': completedBookings,
        'cancelledBookings': cancelledBookings,
        'totalSpent': totalSpent,
      };
    } catch (e) {
      throw 'Error fetching booking statistics: $e';
    }
  }

  // Get upcoming bookings
  Future<List<Booking>> getUpcomingBookings({String? userId}) async {
    try {
      String uid = userId ?? _auth.currentUser?.uid ?? '';
      if (uid.isEmpty) {
        return [];
      }

      DateTime now = DateTime.now();
      QuerySnapshot snapshot = await _firestore
          .collection('bookings')
          .where('userId', isEqualTo: uid)
          .where('checkInDate', isGreaterThan: Timestamp.fromDate(now))
          .where('status', whereIn: ['confirmed', 'pending'])
          .orderBy('checkInDate')
          .get();

      return snapshot.docs.map((doc) => Booking.fromFirestore(doc)).toList();
    } catch (e) {
      throw 'Error fetching upcoming bookings: $e';
    }
  }

  // Get past bookings
  Future<List<Booking>> getPastBookings({String? userId}) async {
    try {
      String uid = userId ?? _auth.currentUser?.uid ?? '';
      if (uid.isEmpty) {
        return [];
      }

      DateTime now = DateTime.now();
      QuerySnapshot snapshot = await _firestore
          .collection('bookings')
          .where('userId', isEqualTo: uid)
          .where('checkOutDate', isLessThan: Timestamp.fromDate(now))
          .orderBy('checkOutDate', descending: true)
          .get();

      return snapshot.docs.map((doc) => Booking.fromFirestore(doc)).toList();
    } catch (e) {
      throw 'Error fetching past bookings: $e';
    }
  }
}
