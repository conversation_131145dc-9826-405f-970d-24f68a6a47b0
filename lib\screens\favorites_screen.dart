import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/hotel.dart';
import '../services/hotel_service.dart';
import '../providers/app_provider.dart';
import '../theme/color.dart';
import '../widgets/hotel_card.dart';
import 'hotel_detail_screen.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({Key? key}) : super(key: key);

  @override
  _FavoritesScreenState createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  final HotelService _hotelService = HotelService();
  List<Hotel> _favoriteHotels = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadFavoriteHotels();
  }

  Future<void> _loadFavoriteHotels() async {
    setState(() {
      _isLoading = true;
    });

    try {
      AppProvider appProvider = Provider.of<AppProvider>(context, listen: false);
      List<String> favoriteIds = appProvider.favoriteHotelIds;
      
      List<Hotel> hotels = [];
      for (String hotelId in favoriteIds) {
        Hotel? hotel = await _hotelService.getHotelById(hotelId);
        if (hotel != null) {
          hotels.add(hotel);
        }
      }
      
      setState(() {
        _favoriteHotels = hotels;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading favorites: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.appBgColor,
      appBar: AppBar(
        backgroundColor: AppColor.appBarColor,
        elevation: 0,
        title: Text(
          'Favorites',
          style: TextStyle(
            color: AppColor.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColor.textColor),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _favoriteHotels.isEmpty
              ? _buildEmptyState()
              : RefreshIndicator(
                  onRefresh: _loadFavoriteHotels,
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _favoriteHotels.length,
                    itemBuilder: (context, index) {
                      Hotel hotel = _favoriteHotels[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Consumer<AppProvider>(
                          builder: (context, appProvider, child) {
                            return HotelCard(
                              hotel: hotel,
                              isFavorite: appProvider.isFavorite(hotel.id),
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => HotelDetailScreen(hotel: hotel),
                                  ),
                                );
                              },
                              onFavorite: () async {
                                await appProvider.toggleFavorite(hotel.id);
                                // Remove from list if no longer favorite
                                if (!appProvider.isFavorite(hotel.id)) {
                                  setState(() {
                                    _favoriteHotels.removeAt(index);
                                  });
                                }
                              },
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 64,
            color: AppColor.labelColor,
          ),
          const SizedBox(height: 16),
          Text(
            'No Favorites Yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColor.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start adding hotels to your favorites\nto see them here',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: AppColor.labelColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Explore Hotels',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
