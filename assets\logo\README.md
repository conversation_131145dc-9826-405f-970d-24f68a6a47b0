# App Logo Assets

## Required Logo File

Please add your app logo as `appicon.png` in this directory.

### Logo Specifications:
- **File Name**: `appicon.png`
- **Format**: PNG with transparent background
- **Recommended Size**: 512x512 pixels (minimum)
- **Aspect Ratio**: 1:1 (square)
- **Background**: Transparent or white
- **Style**: Clean, professional hotel/travel themed logo

### Usage:
This logo will be used in:
- Email templates (EmailJS)
- App launcher icon
- Splash screen
- About page
- Email signatures

### File Location:
```
assets/
└── logo/
    └── appicon.png  ← Add your logo here
```

### After adding the logo:
1. Make sure the file is named exactly `appicon.png`
2. The logo should represent your hotel booking app
3. Ensure it looks good on both light and dark backgrounds
4. Test the logo in email templates by updating the image URL

### Current Status:
❌ Logo file missing - Please add `appicon.png` to this directory
