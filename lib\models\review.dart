import 'package:cloud_firestore/cloud_firestore.dart';

class Review {
  final String id;
  final String userId;
  final String userName;
  final String userProfileImage;
  final String hotelId;
  final String bookingId;
  final double rating;
  final String title;
  final String comment;
  final List<String> images;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isVerified;
  final Map<String, int> helpfulVotes; // userId -> vote (1 for helpful, -1 for not helpful)

  Review({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userProfileImage,
    required this.hotelId,
    required this.bookingId,
    required this.rating,
    required this.title,
    required this.comment,
    required this.images,
    required this.createdAt,
    required this.updatedAt,
    required this.isVerified,
    required this.helpfulVotes,
  });

  factory Review.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return Review(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userProfileImage: data['userProfileImage'] ?? '',
      hotelId: data['hotelId'] ?? '',
      bookingId: data['bookingId'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      title: data['title'] ?? '',
      comment: data['comment'] ?? '',
      images: List<String>.from(data['images'] ?? []),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isVerified: data['isVerified'] ?? false,
      helpfulVotes: Map<String, int>.from(data['helpfulVotes'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userName': userName,
      'userProfileImage': userProfileImage,
      'hotelId': hotelId,
      'bookingId': bookingId,
      'rating': rating,
      'title': title,
      'comment': comment,
      'images': images,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isVerified': isVerified,
      'helpfulVotes': helpfulVotes,
    };
  }

  int get helpfulCount {
    return helpfulVotes.values.where((vote) => vote == 1).length;
  }

  int get notHelpfulCount {
    return helpfulVotes.values.where((vote) => vote == -1).length;
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '${years} year${years > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '${months} month${months > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  Review copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userProfileImage,
    String? hotelId,
    String? bookingId,
    double? rating,
    String? title,
    String? comment,
    List<String>? images,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    Map<String, int>? helpfulVotes,
  }) {
    return Review(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImage: userProfileImage ?? this.userProfileImage,
      hotelId: hotelId ?? this.hotelId,
      bookingId: bookingId ?? this.bookingId,
      rating: rating ?? this.rating,
      title: title ?? this.title,
      comment: comment ?? this.comment,
      images: images ?? this.images,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      helpfulVotes: helpfulVotes ?? this.helpfulVotes,
    );
  }
}

class ReviewSummary {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution; // rating -> count
  final List<String> topPositiveKeywords;
  final List<String> topNegativeKeywords;

  ReviewSummary({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
    required this.topPositiveKeywords,
    required this.topNegativeKeywords,
  });

  factory ReviewSummary.fromReviews(List<Review> reviews) {
    if (reviews.isEmpty) {
      return ReviewSummary(
        averageRating: 0.0,
        totalReviews: 0,
        ratingDistribution: {},
        topPositiveKeywords: [],
        topNegativeKeywords: [],
      );
    }

    double totalRating = 0.0;
    Map<int, int> distribution = {};

    for (var review in reviews) {
      totalRating += review.rating;
      int ratingInt = review.rating.round();
      distribution[ratingInt] = (distribution[ratingInt] ?? 0) + 1;
    }

    return ReviewSummary(
      averageRating: totalRating / reviews.length,
      totalReviews: reviews.length,
      ratingDistribution: distribution,
      topPositiveKeywords: [], // TODO: Implement keyword extraction
      topNegativeKeywords: [], // TODO: Implement keyword extraction
    );
  }
}
