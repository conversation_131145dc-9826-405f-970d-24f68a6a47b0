import 'package:intl/intl.dart';

class CurrencyFormatter {
  static const String rupeeSymbol = '₹';
  static const String currencyCode = 'INR';
  
  // Number format for Indian currency
  static final NumberFormat _indianNumberFormat = NumberFormat.currency(
    locale: 'en_IN',
    symbol: rupeeSymbol,
    decimalDigits: 0,
  );
  
  // Number format with decimals
  static final NumberFormat _indianNumberFormatWithDecimals = NumberFormat.currency(
    locale: 'en_IN',
    symbol: rupeeSymbol,
    decimalDigits: 2,
  );

  // Format amount with rupee symbol (no decimals for whole numbers)
  static String formatAmount(double amount) {
    if (amount == amount.roundToDouble()) {
      return _indianNumberFormat.format(amount);
    } else {
      return _indianNumberFormatWithDecimals.format(amount);
    }
  }

  // Format amount with decimals always
  static String formatAmountWithDecimals(double amount) {
    return _indianNumberFormatWithDecimals.format(amount);
  }

  // Format amount without symbol (just the number)
  static String formatAmountOnly(double amount) {
    final NumberFormat numberFormat = NumberFormat('#,##,###', 'en_IN');
    if (amount == amount.roundToDouble()) {
      return numberFormat.format(amount.round());
    } else {
      return NumberFormat('#,##,##0.00', 'en_IN').format(amount);
    }
  }

  // Get rupee symbol with proper spacing
  static String getRupeeSymbol() {
    return '$rupeeSymbol ';
  }

  // Format for display in lists (compact format)
  static String formatCompact(double amount) {
    if (amount >= 100000) {
      return '$rupeeSymbol${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '$rupeeSymbol${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return formatAmount(amount);
    }
  }

  // Format for price ranges
  static String formatPriceRange(double minPrice, double maxPrice) {
    return '${formatAmount(minPrice)} - ${formatAmount(maxPrice)}';
  }

  // Format with "per night" suffix
  static String formatPerNight(double amount) {
    return '${formatAmount(amount)} per night';
  }

  // Format with "per person" suffix
  static String formatPerPerson(double amount) {
    return '${formatAmount(amount)} per person';
  }

  // Format for total amount with tax breakdown
  static String formatWithTax(double baseAmount, double taxAmount) {
    final total = baseAmount + taxAmount;
    return '''
Base Amount: ${formatAmount(baseAmount)}
Tax: ${formatAmount(taxAmount)}
Total: ${formatAmount(total)}''';
  }

  // Format for booking summary
  static String formatBookingSummary({
    required double roomPrice,
    required int nights,
    required double taxAmount,
    required double serviceCharge,
  }) {
    final subtotal = roomPrice * nights;
    final total = subtotal + taxAmount + serviceCharge;
    
    return '''
Room (${nights} night${nights > 1 ? 's' : ''}): ${formatAmount(subtotal)}
Tax: ${formatAmount(taxAmount)}
Service Charge: ${formatAmount(serviceCharge)}
Total: ${formatAmount(total)}''';
  }

  // Parse amount from formatted string
  static double parseAmount(String formattedAmount) {
    // Remove rupee symbol and commas
    String cleanAmount = formattedAmount
        .replaceAll(rupeeSymbol, '')
        .replaceAll(',', '')
        .trim();
    
    return double.tryParse(cleanAmount) ?? 0.0;
  }

  // Check if amount is in valid range
  static bool isValidAmount(double amount) {
    return amount > 0 && amount <= 1000000; // Max 10 lakh
  }

  // Format for payment gateway
  static String formatForPayment(double amount) {
    // Payment gateways usually need amount in paise (multiply by 100)
    final amountInPaise = (amount * 100).round();
    return amountInPaise.toString();
  }

  // Format amount from paise to rupees
  static String formatFromPaise(int amountInPaise) {
    final amountInRupees = amountInPaise / 100.0;
    return formatAmount(amountInRupees);
  }

  // Format for different contexts
  static String formatForContext(double amount, CurrencyContext context) {
    switch (context) {
      case CurrencyContext.list:
        return formatCompact(amount);
      case CurrencyContext.detail:
        return formatAmount(amount);
      case CurrencyContext.booking:
        return formatAmountWithDecimals(amount);
      case CurrencyContext.payment:
        return formatAmount(amount);
      case CurrencyContext.receipt:
        return formatAmountWithDecimals(amount);
    }
  }

  // Get currency info
  static Map<String, String> getCurrencyInfo() {
    return {
      'symbol': rupeeSymbol,
      'code': currencyCode,
      'name': 'Indian Rupee',
      'locale': 'en_IN',
    };
  }

  // Format for email templates
  static String formatForEmail(double amount) {
    return formatAmountWithDecimals(amount);
  }

  // Format large amounts with proper Indian numbering
  static String formatLargeAmount(double amount) {
    if (amount >= 10000000) { // 1 crore
      return '$rupeeSymbol${(amount / 10000000).toStringAsFixed(2)} Cr';
    } else if (amount >= 100000) { // 1 lakh
      return '$rupeeSymbol${(amount / 100000).toStringAsFixed(2)} L';
    } else if (amount >= 1000) { // 1 thousand
      return '$rupeeSymbol${(amount / 1000).toStringAsFixed(1)} K';
    } else {
      return formatAmount(amount);
    }
  }

  // Validate and format user input
  static String? validateAndFormat(String input) {
    // Remove any existing formatting
    String cleanInput = input
        .replaceAll(rupeeSymbol, '')
        .replaceAll(',', '')
        .trim();
    
    final amount = double.tryParse(cleanInput);
    if (amount == null || !isValidAmount(amount)) {
      return null;
    }
    
    return formatAmount(amount);
  }
}

// Context enum for different formatting needs
enum CurrencyContext {
  list,      // For hotel lists (compact)
  detail,    // For detail screens
  booking,   // For booking screens
  payment,   // For payment screens
  receipt,   // For receipts and confirmations
}

// Extension for easy currency formatting on double
extension CurrencyExtension on double {
  String toRupees() => CurrencyFormatter.formatAmount(this);
  String toRupeesWithDecimals() => CurrencyFormatter.formatAmountWithDecimals(this);
  String toRupeesCompact() => CurrencyFormatter.formatCompact(this);
  String toRupeesPerNight() => CurrencyFormatter.formatPerNight(this);
  String toRupeesLarge() => CurrencyFormatter.formatLargeAmount(this);
}

// Extension for easy currency formatting on int
extension CurrencyIntExtension on int {
  String toRupees() => CurrencyFormatter.formatAmount(toDouble());
  String toRupeesFromPaise() => CurrencyFormatter.formatFromPaise(this);
}
