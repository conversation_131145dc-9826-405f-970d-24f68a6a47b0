rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Hotels collection - Allow read access to all users, write access to authenticated users only
    match /hotels/{hotelId} {
      allow read: if true; // Anyone can read hotel data
      allow write: if request.auth != null && request.auth.token.admin == true; // Only admins can write
    }
    
    // Rooms collection - Allow read access to all users, write access to authenticated users only
    match /rooms/{roomId} {
      allow read: if true; // Anyone can read room data
      allow write: if request.auth != null && request.auth.token.admin == true; // Only admins can write
    }
    
    // Users collection - Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null && request.auth.uid == userId;
    }
    
    // Bookings collection - Users can only access their own bookings
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.userId || 
         request.auth.token.admin == true);
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Reviews collection - Allow read to all, write to authenticated users
    match /reviews/{reviewId} {
      allow read: if true; // Anyone can read reviews
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
      allow update, delete: if request.auth != null && 
        (request.auth.uid == resource.data.userId || 
         request.auth.token.admin == true);
    }
    
    // Favorites collection - Users can only access their own favorites
    match /favorites/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Sample data initialization flag
    match /system/initialized {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Admin functions
    match /admin/{document=**} {
      allow read, write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Analytics and app metrics
    match /analytics/{document=**} {
      allow read: if request.auth != null && request.auth.token.admin == true;
      allow write: if request.auth != null;
    }
  }
}
