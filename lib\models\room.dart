import 'package:cloud_firestore/cloud_firestore.dart';

class Room {
  final String id;
  final String hotelId;
  final String name;
  final String description;
  final String type;
  final List<String> images;
  final double pricePerNight;
  final String currency;
  final int maxOccupancy;
  final List<String> amenities;
  final bool isAvailable;
  final int availableRooms;
  final double size; // in square meters
  final String bedType;
  final bool hasBalcony;
  final bool hasKitchen;
  final String view; // e.g., "Sea View", "City View", "Garden View"

  Room({
    required this.id,
    required this.hotelId,
    required this.name,
    required this.description,
    required this.type,
    required this.images,
    required this.pricePerNight,
    required this.currency,
    required this.maxOccupancy,
    required this.amenities,
    required this.isAvailable,
    required this.availableRooms,
    this.size = 0.0,
    this.bedType = '',
    this.hasBalcony = false,
    this.hasKitchen = false,
    this.view = '',
  });

  factory Room.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return Room(
      id: doc.id,
      hotelId: data['hotelId'] ?? '',
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      type: data['type'] ?? '',
      images: List<String>.from(data['images'] ?? []),
      pricePerNight: (data['pricePerNight'] ?? 0.0).toDouble(),
      currency: data['currency'] ?? 'USD',
      maxOccupancy: data['maxOccupancy'] ?? 1,
      amenities: List<String>.from(data['amenities'] ?? []),
      isAvailable: data['isAvailable'] ?? true,
      availableRooms: data['availableRooms'] ?? 0,
      size: (data['size'] ?? 0.0).toDouble(),
      bedType: data['bedType'] ?? '',
      hasBalcony: data['hasBalcony'] ?? false,
      hasKitchen: data['hasKitchen'] ?? false,
      view: data['view'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'hotelId': hotelId,
      'name': name,
      'description': description,
      'type': type,
      'images': images,
      'pricePerNight': pricePerNight,
      'currency': currency,
      'maxOccupancy': maxOccupancy,
      'amenities': amenities,
      'isAvailable': isAvailable,
      'availableRooms': availableRooms,
      'size': size,
      'bedType': bedType,
      'hasBalcony': hasBalcony,
      'hasKitchen': hasKitchen,
      'view': view,
    };
  }

  Room copyWith({
    String? id,
    String? hotelId,
    String? name,
    String? description,
    String? type,
    List<String>? images,
    double? pricePerNight,
    String? currency,
    int? maxOccupancy,
    List<String>? amenities,
    bool? isAvailable,
    int? availableRooms,
    double? size,
    String? bedType,
    bool? hasBalcony,
    bool? hasKitchen,
    String? view,
  }) {
    return Room(
      id: id ?? this.id,
      hotelId: hotelId ?? this.hotelId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      images: images ?? this.images,
      pricePerNight: pricePerNight ?? this.pricePerNight,
      currency: currency ?? this.currency,
      maxOccupancy: maxOccupancy ?? this.maxOccupancy,
      amenities: amenities ?? this.amenities,
      isAvailable: isAvailable ?? this.isAvailable,
      availableRooms: availableRooms ?? this.availableRooms,
      size: size ?? this.size,
      bedType: bedType ?? this.bedType,
      hasBalcony: hasBalcony ?? this.hasBalcony,
      hasKitchen: hasKitchen ?? this.hasKitchen,
      view: view ?? this.view,
    );
  }
}
