<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed - One Touch Hotel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #f44336;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .title {
            color: #f44336;
            font-size: 28px;
            font-weight: bold;
            margin: 0;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
            margin: 5px 0 0 0;
        }
        .failure-details {
            background-color: #ffebee;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #f44336;
        }
        .booking-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .detail-value {
            color: #333;
        }
        .solutions {
            background-color: #fff3e0;
            border: 1px solid #ffb74d;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .urgent-notice {
            background-color: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .contact-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #f44336;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 5px;
        }
        .btn-primary {
            background-color: #2196F3;
        }
        @media (max-width: 600px) {
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">❌ Payment Failed</h1>
            <p class="subtitle">We couldn't process your payment</p>
        </div>

        <p>Dear {{customer_name}},</p>
        
        <p>We're sorry to inform you that your payment for booking <strong>{{booking_id}}</strong> could not be processed at this time.</p>

        <div class="failure-details">
            <h3 style="color: #f44336; margin-top: 0;">💳 Payment Failure Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Booking ID:</span>
                <span class="detail-value">{{booking_id}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Failure Reason:</span>
                <span class="detail-value">{{failure_reason}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Attempted Amount:</span>
                <span class="detail-value">{{amount}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Attempt Time:</span>
                <span class="detail-value">{{attempt_time}}</span>
            </div>
        </div>

        <div class="urgent-notice">
            <h4 style="margin-top: 0; color: #f57c00;">⏰ Your booking is reserved for 30 minutes</h4>
            <p style="margin: 5px 0;">Please complete the payment within 30 minutes to secure your reservation.</p>
        </div>

        <div class="solutions">
            <h4 style="margin-top: 0; color: #f57c00;">🔧 What you can do:</h4>
            <ol style="margin: 10px 0; padding-left: 20px;">
                <li><strong>Try again:</strong> Attempt the payment with the same or different payment method</li>
                <li><strong>Check your card:</strong> Ensure sufficient balance and card validity</li>
                <li><strong>Use different method:</strong> Try UPI, Net Banking, or Digital Wallet</li>
                <li><strong>Contact bank:</strong> If the issue persists, contact your bank</li>
                <li><strong>Get help:</strong> Reach out to our support team for assistance</li>
            </ol>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <p style="font-size: 18px; color: #f44336; font-weight: bold;">
                Don't worry - we're here to help you complete your booking!
            </p>
        </div>

        <div class="booking-details">
            <h3 style="color: #2196F3; margin-top: 0;">🏨 Booking Details (Still Reserved)</h3>
            
            <div class="detail-row">
                <span class="detail-label">Hotel:</span>
                <span class="detail-value">{{hotel_name}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Room:</span>
                <span class="detail-value">{{room_name}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Check-in:</span>
                <span class="detail-value">{{check_in_date}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Check-out:</span>
                <span class="detail-value">{{check_out_date}}</span>
            </div>
        </div>

        <div class="contact-info">
            <h4 style="margin-top: 0; color: #1976d2;">📞 Need Immediate Help?</h4>
            <p style="margin: 5px 0;"><strong>24/7 Support Email:</strong> {{support_email}}</p>
            <p style="margin: 5px 0;"><strong>Support Phone:</strong> {{support_phone}}</p>
            <p style="margin: 5px 0;"><strong>WhatsApp Support:</strong> {{support_phone}}</p>
            <p style="margin: 5px 0;"><strong>Live Chat:</strong> Available on our app</p>
        </div>

        <p>We apologize for the inconvenience and are here to help you complete your booking successfully.</p>

        <div class="footer">
            <p><strong>One Touch Hotel Booking</strong></p>
            <p>Secure payments powered by Cashfree</p>
            <p style="font-size: 12px; color: #999;">
                This is an automated email. Please do not reply to this email address.
            </p>
        </div>
    </div>
</body>
</html>
