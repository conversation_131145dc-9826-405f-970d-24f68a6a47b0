import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/hotel.dart';
import '../models/review.dart';
import '../services/review_service.dart';
import '../services/auth_service.dart';
import '../theme/color.dart';
import '../widgets/review_card.dart';
import '../widgets/custom_button.dart';
import 'add_review_screen.dart';

class HotelReviewsScreen extends StatefulWidget {
  final Hotel hotel;
  final String? bookingId;

  const HotelReviewsScreen({
    Key? key,
    required this.hotel,
    this.bookingId,
  }) : super(key: key);

  @override
  State<HotelReviewsScreen> createState() => _HotelReviewsScreenState();
}

class _HotelReviewsScreenState extends State<HotelReviewsScreen> {
  final ReviewService _reviewService = ReviewService();
  final AuthService _authService = AuthService();
  
  ReviewSummary? _reviewSummary;
  bool _canUserReview = false;
  bool _isLoadingSummary = true;
  String _sortBy = 'newest';

  @override
  void initState() {
    super.initState();
    _loadReviewSummary();
    _checkUserReviewPermission();
  }

  Future<void> _loadReviewSummary() async {
    try {
      ReviewSummary summary = await _reviewService.getReviewSummary(widget.hotel.id);
      setState(() {
        _reviewSummary = summary;
        _isLoadingSummary = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingSummary = false;
      });
    }
  }

  Future<void> _checkUserReviewPermission() async {
    if (_authService.isAnonymous) {
      setState(() {
        _canUserReview = false;
      });
      return;
    }

    bool canReview = await _reviewService.canUserReviewHotel(widget.hotel.id);
    setState(() {
      _canUserReview = canReview;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.appBgColor,
      appBar: AppBar(
        backgroundColor: AppColor.appBarColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColor.textColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Reviews',
          style: TextStyle(
            color: AppColor.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          PopupMenuButton<String>(
            icon: Icon(Icons.sort, color: AppColor.textColor),
            onSelected: (value) {
              setState(() {
                _sortBy = value;
              });
            },
            itemBuilder: (context) => [
              PopupMenuItem(value: 'newest', child: Text('Newest First')),
              PopupMenuItem(value: 'oldest', child: Text('Oldest First')),
              PopupMenuItem(value: 'highest', child: Text('Highest Rating')),
              PopupMenuItem(value: 'lowest', child: Text('Lowest Rating')),
              PopupMenuItem(value: 'helpful', child: Text('Most Helpful')),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Review summary header
          if (_isLoadingSummary)
            Container(
              padding: const EdgeInsets.all(20),
              child: Center(child: CircularProgressIndicator()),
            )
          else if (_reviewSummary != null)
            _buildReviewSummary(),

          // Write review button
          if (_canUserReview)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: CustomButton(
                text: 'Write a Review',
                onPressed: () => _navigateToAddReview(),
                icon: Icon(Icons.rate_review, color: Colors.white),
              ),
            ),

          // Reviews list
          Expanded(
            child: StreamBuilder<List<Review>>(
              stream: _reviewService.getHotelReviews(widget.hotel.id),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: Colors.grey),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading reviews',
                          style: TextStyle(color: Colors.grey, fontSize: 16),
                        ),
                      ],
                    ),
                  );
                }

                List<Review> reviews = snapshot.data ?? [];
                
                if (reviews.isEmpty) {
                  return _buildEmptyState();
                }

                // Sort reviews
                _sortReviews(reviews);

                return ListView.builder(
                  padding: const EdgeInsets.all(20),
                  itemCount: reviews.length,
                  itemBuilder: (context, index) {
                    Review review = reviews[index];
                    bool isCurrentUserReview = _authService.currentUser?.uid == review.userId;
                    
                    return ReviewCard(
                      review: review,
                      canInteract: !_authService.isAnonymous,
                      onEdit: isCurrentUserReview ? () => _editReview(review) : null,
                      onDelete: isCurrentUserReview ? () => _deleteReview(review) : null,
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewSummary() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColor.shadowColor.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Overall rating
              Column(
                children: [
                  Text(
                    _reviewSummary!.averageRating.toStringAsFixed(1),
                    style: TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      color: AppColor.primary,
                    ),
                  ),
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < _reviewSummary!.averageRating.floor()
                            ? Icons.star
                            : index < _reviewSummary!.averageRating
                                ? Icons.star_half
                                : Icons.star_border,
                        color: Colors.amber,
                        size: 20,
                      );
                    }),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_reviewSummary!.totalReviews} reviews',
                    style: TextStyle(
                      color: AppColor.labelColor,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 30),
              
              // Rating distribution
              Expanded(
                child: Column(
                  children: List.generate(5, (index) {
                    int rating = 5 - index;
                    int count = _reviewSummary!.ratingDistribution[rating] ?? 0;
                    double percentage = _reviewSummary!.totalReviews > 0 
                        ? count / _reviewSummary!.totalReviews 
                        : 0.0;
                    
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        children: [
                          Text(
                            '$rating',
                            style: TextStyle(fontSize: 12, color: AppColor.textColor),
                          ),
                          Icon(Icons.star, size: 12, color: Colors.amber),
                          const SizedBox(width: 8),
                          Expanded(
                            child: LinearProgressIndicator(
                              value: percentage,
                              backgroundColor: Colors.grey.shade200,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColor.primary),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '$count',
                            style: TextStyle(fontSize: 12, color: AppColor.labelColor),
                          ),
                        ],
                      ),
                    );
                  }),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.rate_review_outlined,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'No reviews yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColor.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Be the first to share your experience!',
            style: TextStyle(
              color: AppColor.labelColor,
              fontSize: 14,
            ),
          ),
          if (_canUserReview) ...[
            const SizedBox(height: 20),
            CustomButton(
              text: 'Write First Review',
              onPressed: () => _navigateToAddReview(),
              width: 200,
            ),
          ],
        ],
      ),
    );
  }

  void _sortReviews(List<Review> reviews) {
    switch (_sortBy) {
      case 'oldest':
        reviews.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case 'highest':
        reviews.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'lowest':
        reviews.sort((a, b) => a.rating.compareTo(b.rating));
        break;
      case 'helpful':
        reviews.sort((a, b) => b.helpfulCount.compareTo(a.helpfulCount));
        break;
      case 'newest':
      default:
        reviews.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
    }
  }

  Future<void> _navigateToAddReview() async {
    if (_authService.isAnonymous) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please create an account to write reviews'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddReviewScreen(
          hotel: widget.hotel,
          bookingId: widget.bookingId ?? '',
        ),
      ),
    );

    if (result == true) {
      _loadReviewSummary();
      _checkUserReviewPermission();
    }
  }

  Future<void> _editReview(Review review) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddReviewScreen(
          hotel: widget.hotel,
          bookingId: review.bookingId,
          existingReview: review,
        ),
      ),
    );

    if (result == true) {
      _loadReviewSummary();
    }
  }

  Future<void> _deleteReview(Review review) async {
    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Review'),
        content: Text('Are you sure you want to delete this review?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _reviewService.deleteReview(review.id);
        _loadReviewSummary();
        _checkUserReviewPermission();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Review deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
