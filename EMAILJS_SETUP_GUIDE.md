# 📧 EmailJS Setup Guide for One Touch Hotel App

## 🚀 **Complete EmailJS Integration Guide**

This guide will help you set up EmailJS for your hotel booking app to send professional email notifications.

## 📋 **Step 1: Create EmailJS Account**

1. **Visit EmailJS**: Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. **Sign Up**: Create a free account (100 emails/month free)
3. **Verify Email**: Confirm your email address

## 🔧 **Step 2: Create Email Service**

1. **Go to Email Services**: In your EmailJS dashboard
2. **Add New Service**: Click "Add New Service"
3. **Choose Provider**: Select your email provider (Gmail, Outlook, etc.)
4. **Service ID**: Use `service_one_touch_hotel`
5. **Configure**: Follow the setup for your email provider

### **Recommended Email Providers:**
- **Gmail**: Easy setup, reliable
- **Outlook**: Good for business emails
- **SendGrid**: Professional email service

## 📝 **Step 3: Create Email Templates**

Create these 4 templates in your EmailJS dashboard:

### **Template 1: Booking Confirmation**
- **Template ID**: `template_booking_confirmation`
- **Template Name**: "Hotel Booking Confirmation"
- **HTML Content**: Use the content from `email_templates/booking_confirmation.html`

### **Template 2: Payment Success**
- **Template ID**: `template_payment_success`
- **Template Name**: "Payment Successful"
- **HTML Content**: Use the content from `email_templates/payment_success.html`

### **Template 3: Payment Failed**
- **Template ID**: `template_payment_failed`
- **Template Name**: "Payment Failed"
- **HTML Content**: Use the content from `email_templates/payment_failed.html`

### **Template 4: Booking Cancellation**
- **Template ID**: `template_booking_cancellation`
- **Template Name**: "Booking Cancellation"
- **HTML Content**: Create a cancellation template (similar structure)

## 🔑 **Step 4: Get API Keys**

1. **Public Key**: Go to Account → API Keys → Public Key
2. **Private Key**: Go to Account → API Keys → Private Key (if needed)
3. **Copy Keys**: Save these securely

## 📱 **Step 5: Update App Configuration**

Update `lib/services/email_service.dart` with your actual values:

```dart
class EmailService {
  // Replace these with your actual EmailJS values
  static const String _serviceId = 'service_one_touch_hotel'; // Your service ID
  static const String _bookingTemplateId = 'template_booking_confirmation';
  static const String _paymentSuccessTemplateId = 'template_payment_success';
  static const String _paymentFailedTemplateId = 'template_payment_failed';
  static const String _cancellationTemplateId = 'template_booking_cancellation';
  static const String _publicKey = 'YOUR_ACTUAL_PUBLIC_KEY'; // Replace this
  static const String _privateKey = 'YOUR_ACTUAL_PRIVATE_KEY'; // Replace this
}
```

## 🖼️ **Step 6: Add App Logo**

1. **Add Logo**: Place your `appicon.png` in `assets/logo/` directory
2. **Update Templates**: Replace logo URL in email templates:
   ```html
   <img src="https://your-domain.com/assets/logo/appicon.png" alt="One Touch Hotel" class="logo">
   ```
3. **Host Logo**: Upload logo to a public URL or use base64 encoding

## 🧪 **Step 7: Test Email Templates**

### **Template Variables to Test:**

#### **Booking Confirmation Variables:**
```javascript
{
  "customer_name": "John Doe",
  "booking_id": "HTL123456",
  "hotel_name": "The Taj Mahal Palace",
  "room_name": "Deluxe King Room",
  "check_in_date": "15 Dec 2024",
  "check_out_date": "18 Dec 2024",
  "guests": "2",
  "amount": "₹15,000.00"
}
```

#### **Payment Success Variables:**
```javascript
{
  "customer_name": "John Doe",
  "booking_id": "HTL123456",
  "order_id": "ORDER_HTL123456_1703123456789",
  "hotel_name": "The Taj Mahal Palace",
  "room_name": "Deluxe King Room",
  "check_in_date": "15 Dec 2024",
  "check_out_date": "18 Dec 2024",
  "amount": "₹15,000.00",
  "payment_date": "12 Dec 2024, 02:30 PM"
}
```

#### **Payment Failed Variables:**
```javascript
{
  "customer_name": "John Doe",
  "booking_id": "HTL123456",
  "failure_reason": "Insufficient funds",
  "amount": "₹15,000.00",
  "attempt_time": "12 Dec 2024, 02:30 PM",
  "hotel_name": "The Taj Mahal Palace",
  "room_name": "Deluxe King Room",
  "check_in_date": "15 Dec 2024",
  "check_out_date": "18 Dec 2024",
  "support_email": "<EMAIL>",
  "support_phone": "+91-1234567890"
}
```

## 🔄 **Step 8: Enable EmailJS in App**

1. **Uncomment EmailJS**: In `pubspec.yaml`, uncomment:
   ```yaml
   emailjs: ^4.0.0
   ```

2. **Run pub get**:
   ```bash
   flutter pub get
   ```

3. **Update Email Service**: Replace simulated methods with actual EmailJS calls

## 📊 **Step 9: Monitor Email Delivery**

1. **EmailJS Dashboard**: Monitor sent emails
2. **Delivery Reports**: Check success/failure rates
3. **Usage Limits**: Monitor monthly quota
4. **Error Logs**: Check for delivery issues

## 🔒 **Step 10: Security Best Practices**

1. **Environment Variables**: Store keys in environment variables
2. **Rate Limiting**: Implement email sending limits
3. **Validation**: Validate email addresses before sending
4. **Error Handling**: Handle EmailJS API errors gracefully

## 📈 **Step 11: Upgrade for Production**

### **Free Plan Limitations:**
- 100 emails/month
- EmailJS branding
- Basic support

### **Paid Plans Benefits:**
- More emails (1,000-50,000+/month)
- Remove EmailJS branding
- Priority support
- Advanced analytics

## 🛠️ **Troubleshooting**

### **Common Issues:**

1. **Template Not Found**:
   - Check template ID spelling
   - Ensure template is published

2. **Service Not Working**:
   - Verify service configuration
   - Check email provider settings

3. **Variables Not Showing**:
   - Check variable names in template
   - Ensure proper {{variable}} syntax

4. **Emails Not Sending**:
   - Check API keys
   - Verify service status
   - Check email quotas

## 📞 **Support Contacts**

- **EmailJS Support**: [https://www.emailjs.com/docs/](https://www.emailjs.com/docs/)
- **Community Forum**: EmailJS community discussions
- **Documentation**: Comprehensive API documentation

## ✅ **Checklist**

- [ ] EmailJS account created
- [ ] Email service configured
- [ ] 4 email templates created
- [ ] API keys obtained
- [ ] App configuration updated
- [ ] Logo added to assets
- [ ] Templates tested
- [ ] EmailJS enabled in app
- [ ] Production deployment ready

## 🎯 **Final Result**

After completing this setup, your hotel booking app will send:
- ✅ Professional booking confirmations
- ✅ Payment success notifications
- ✅ Payment failure alerts
- ✅ Booking cancellation confirmations
- ✅ Branded emails with your logo
- ✅ Mobile-responsive email templates

Your users will receive beautiful, professional emails for all booking-related activities!
