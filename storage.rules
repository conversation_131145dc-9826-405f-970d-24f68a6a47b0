rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Hotel images - Allow read to all, write to authenticated users only
    match /hotels/{hotelId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Room images - Allow read to all, write to authenticated users only
    match /rooms/{roomId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // User profile images - Users can only access their own images
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Review images - Allow read to all, write to authenticated users for their own reviews
    match /reviews/{reviewId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // App assets and general images
    match /assets/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Temporary uploads
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
