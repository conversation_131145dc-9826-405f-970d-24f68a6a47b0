import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';

class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  // Initialize performance optimizations
  static Future<void> initialize() async {
    // Enable hardware acceleration
    if (!kIsWeb) {
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
      );
    }

    // Optimize memory usage
    _optimizeMemoryUsage();

    // Set preferred orientations for better performance
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Optimize system UI
    _optimizeSystemUI();

    print('Performance optimizations initialized');
  }

  // Optimize memory usage
  static void _optimizeMemoryUsage() {
    // Enable memory pressure callbacks
    WidgetsBinding.instance.addObserver(_MemoryPressureObserver());
    
    // Set image cache size
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50MB
  }

  // Optimize system UI
  static void _optimizeSystemUI() {
    if (!kIsWeb) {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );
    }
  }

  // Preload critical resources
  static Future<void> preloadCriticalResources(BuildContext context) async {
    // Preload common images
    final List<String> criticalImages = [
      'assets/icons/hotel.png',
      'assets/icons/location.png',
      'assets/icons/star.png',
    ];

    for (String imagePath in criticalImages) {
      try {
        await precacheImage(AssetImage(imagePath), context);
      } catch (e) {
        debugPrint('Failed to preload image: $imagePath');
      }
    }
  }

  // Optimize list performance
  static Widget optimizedListView({
    required List<Widget> children,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
  }) {
    return ListView.builder(
      controller: controller,
      physics: physics ?? const BouncingScrollPhysics(),
      padding: padding,
      itemCount: children.length,
      cacheExtent: 1000, // Cache more items for smoother scrolling
      itemBuilder: (context, index) => children[index],
    );
  }

  // Optimize grid performance
  static Widget optimizedGridView({
    required List<Widget> children,
    required int crossAxisCount,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
    double childAspectRatio = 1.0,
  }) {
    return GridView.builder(
      controller: controller,
      physics: physics ?? const BouncingScrollPhysics(),
      padding: padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: children.length,
      cacheExtent: 1000,
      itemBuilder: (context, index) => children[index],
    );
  }

  // Debounce function for search and other frequent operations
  static void debounce(
    String key,
    VoidCallback callback, {
    Duration delay = const Duration(milliseconds: 300),
  }) {
    _DebounceManager.instance.debounce(key, callback, delay: delay);
  }

  // Clear all caches
  static void clearCaches() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  // Get memory usage info
  static Map<String, dynamic> getMemoryInfo() {
    final imageCache = PaintingBinding.instance.imageCache;
    return {
      'imageCacheSize': imageCache.currentSize,
      'imageCacheMaxSize': imageCache.maximumSize,
      'imageCacheSizeBytes': imageCache.currentSizeBytes,
      'imageCacheMaxSizeBytes': imageCache.maximumSizeBytes,
    };
  }

  // Force garbage collection (use sparingly)
  static void forceGarbageCollection() {
    // Clear image cache
    PaintingBinding.instance.imageCache.clear();
    
    // Clear live images
    PaintingBinding.instance.imageCache.clearLiveImages();
    
    print('Forced garbage collection completed');
  }
}

// Memory pressure observer
class _MemoryPressureObserver extends WidgetsBindingObserver {
  @override
  void didHaveMemoryPressure() {
    super.didHaveMemoryPressure();
    
    // Clear caches when memory pressure is detected
    PerformanceService.clearCaches();
    
    debugPrint('Memory pressure detected - cleared caches');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    if (state == AppLifecycleState.paused) {
      // Clear caches when app is paused
      PerformanceService.clearCaches();
    }
  }
}

// Debounce manager for performance optimization
class _DebounceManager {
  static final _DebounceManager instance = _DebounceManager._internal();
  factory _DebounceManager() => instance;
  _DebounceManager._internal();

  final Map<String, Timer?> _timers = {};

  void debounce(String key, VoidCallback callback, {Duration delay = const Duration(milliseconds: 300)}) {
    _timers[key]?.cancel();
    _timers[key] = Timer(delay, callback);
  }

  void cancel(String key) {
    _timers[key]?.cancel();
    _timers.remove(key);
  }

  void cancelAll() {
    for (final timer in _timers.values) {
      timer?.cancel();
    }
    _timers.clear();
  }
}

// Timer class for debouncing
class Timer {
  final Duration duration;
  final VoidCallback callback;
  late final Future _future;

  Timer(this.duration, this.callback) {
    _future = Future.delayed(duration, callback);
  }

  void cancel() {
    // Note: Future.delayed cannot be cancelled in Dart
    // This is a simplified implementation
  }
}

// Performance monitoring widget
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final bool enabled;

  const PerformanceMonitor({
    Key? key,
    required this.child,
    this.enabled = kDebugMode,
  }) : super(key: key);

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: 50,
          right: 10,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Performance Monitor',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                _buildMemoryInfo(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMemoryInfo() {
    final memoryInfo = PerformanceService.getMemoryInfo();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Images: ${memoryInfo['imageCacheSize']}/${memoryInfo['imageCacheMaxSize']}',
          style: const TextStyle(color: Colors.white, fontSize: 10),
        ),
        Text(
          'Memory: ${(memoryInfo['imageCacheSizeBytes'] / 1024 / 1024).toStringAsFixed(1)}MB',
          style: const TextStyle(color: Colors.white, fontSize: 10),
        ),
      ],
    );
  }
}
