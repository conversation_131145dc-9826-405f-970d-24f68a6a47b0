import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../models/hotel.dart';
import '../../services/image_upload_service.dart';
import '../../services/hotel_service.dart';
import '../../theme/color.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/simple_image_gallery.dart';

class HotelImageManagementScreen extends StatefulWidget {
  final Hotel hotel;

  const HotelImageManagementScreen({
    Key? key,
    required this.hotel,
  }) : super(key: key);

  @override
  State<HotelImageManagementScreen> createState() => _HotelImageManagementScreenState();
}

class _HotelImageManagementScreenState extends State<HotelImageManagementScreen> {
  final ImageUploadService _imageUploadService = ImageUploadService();
  final HotelService _hotelService = HotelService();
  
  List<String> _hotelImages = [];
  List<XFile> _newImages = [];
  bool _isLoading = false;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _hotelImages = List.from(widget.hotel.images);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColor.primary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Manage Hotel Images',
          style: TextStyle(
            color: AppColor.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (_newImages.isNotEmpty || _hotelImages.length != widget.hotel.images.length)
            TextButton(
              onPressed: _isUploading ? null : _saveChanges,
              child: _isUploading
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(
                      'Save',
                      style: TextStyle(
                        color: AppColor.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Hotel info
                  _buildHotelInfo(),
                  const SizedBox(height: 24),

                  // Current images section
                  _buildCurrentImagesSection(),
                  const SizedBox(height: 24),

                  // New images section
                  if (_newImages.isNotEmpty) ...[
                    _buildNewImagesSection(),
                    const SizedBox(height: 24),
                  ],

                  // Add images button
                  _buildAddImagesButton(),
                  const SizedBox(height: 24),

                  // Image management tips
                  _buildImageTips(),
                ],
              ),
            ),
    );
  }

  Widget _buildHotelInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColor.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColor.primary.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(Icons.hotel, color: AppColor.primary, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.hotel.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${widget.hotel.city}, ${widget.hotel.country}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentImagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Current Images (${_hotelImages.length})',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (_hotelImages.isNotEmpty)
              TextButton.icon(
                onPressed: _clearAllImages,
                icon: const Icon(Icons.clear_all, size: 16),
                label: const Text('Clear All'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        
        if (_hotelImages.isEmpty)
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.image_not_supported, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 8),
                  Text(
                    'No images available',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          )
        else
          SimpleImageGallery(
            images: _hotelImages,
            height: 250,
            showCounter: true,
            showFullScreenButton: true,
          ),
        
        if (_hotelImages.isNotEmpty) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _hotelImages.asMap().entries.map((entry) {
              final int index = entry.key;
              return _buildImageThumbnail(entry.value, index, isNew: false);
            }).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildNewImagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'New Images (${_newImages.length})',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            TextButton.icon(
              onPressed: _clearNewImages,
              icon: const Icon(Icons.clear, size: 16),
              label: const Text('Clear New'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _newImages.asMap().entries.map((entry) {
            final int index = entry.key;
            return _buildNewImageThumbnail(entry.value, index);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildImageThumbnail(String imageUrl, int index, {required bool isNew}) {
    return Container(
      width: 80,
      height: 80,
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              imageUrl,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 80,
                  height: 80,
                  color: Colors.grey[300],
                  child: Icon(Icons.broken_image, color: Colors.grey[500]),
                );
              },
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => _removeImage(index, isNew: isNew),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewImageThumbnail(XFile imageFile, int index) {
    return Container(
      width: 80,
      height: 80,
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: FutureBuilder<String>(
              future: imageFile.path.isNotEmpty ? Future.value(imageFile.path) : Future.value(''),
              builder: (context, snapshot) {
                if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                  return Image.network(
                    snapshot.data!,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 80,
                        height: 80,
                        color: Colors.green[100],
                        child: Icon(Icons.image, color: Colors.green[600]),
                      );
                    },
                  );
                }
                return Container(
                  width: 80,
                  height: 80,
                  color: Colors.green[100],
                  child: Icon(Icons.image, color: Colors.green[600]),
                );
              },
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => _removeNewImage(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ),
          ),
          // New image indicator
          Positioned(
            bottom: 2,
            left: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'NEW',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 8,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddImagesButton() {
    return CustomButton(
      text: 'Add Images',
      onPressed: _addImages,
      backgroundColor: AppColor.primary.withValues(alpha: 0.1),
      textColor: AppColor.primary,
    );
  }

  Widget _buildImageTips() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'Image Management Tips',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTip('Use high-quality images (minimum 800x600 pixels)'),
          _buildTip('Maximum 10 images per hotel'),
          _buildTip('Supported formats: JPG, PNG, WebP'),
          _buildTip('Maximum file size: 5MB per image'),
          _buildTip('First image will be used as the main hotel image'),
        ],
      ),
    );
  }

  Widget _buildTip(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ', style: TextStyle(color: Colors.blue[600])),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _addImages() async {
    try {
      final List<XFile> images = await _imageUploadService.pickMultipleImages(
        maxImages: 10 - (_hotelImages.length + _newImages.length),
      );

      if (images.isNotEmpty) {
        setState(() {
          _newImages.addAll(images);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error picking images: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _removeImage(int index, {required bool isNew}) {
    setState(() {
      if (isNew) {
        _newImages.removeAt(index);
      } else {
        _hotelImages.removeAt(index);
      }
    });
  }

  void _removeNewImage(int index) {
    setState(() {
      _newImages.removeAt(index);
    });
  }

  void _clearAllImages() {
    setState(() {
      _hotelImages.clear();
    });
  }

  void _clearNewImages() {
    setState(() {
      _newImages.clear();
    });
  }

  Future<void> _saveChanges() async {
    setState(() {
      _isUploading = true;
    });

    try {
      List<String> finalImages = List.from(_hotelImages);

      // Upload new images
      if (_newImages.isNotEmpty) {
        final List<String> uploadedUrls = await _imageUploadService.uploadReviewPhotos(
          userId: 'admin',
          reviewId: 'hotel_${widget.hotel.id}',
          images: _newImages,
        );
        finalImages.addAll(uploadedUrls);
      }

      // Update hotel with new images
      // Note: You'll need to implement updateHotelImages in HotelService
      // await _hotelService.updateHotelImages(widget.hotel.id, finalImages);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Hotel images updated successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      Navigator.pop(context, true);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating images: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }
}
