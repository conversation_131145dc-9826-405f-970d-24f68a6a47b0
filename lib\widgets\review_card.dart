import 'package:flutter/material.dart';
import '../models/review.dart';
import '../theme/color.dart';
import '../services/review_service.dart';
import '../widgets/custom_image.dart';

class ReviewCard extends StatefulWidget {
  final Review review;
  final bool showHotelName;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool canInteract;

  const ReviewCard({
    Key? key,
    required this.review,
    this.showHotelName = false,
    this.onEdit,
    this.onDelete,
    this.canInteract = true,
  }) : super(key: key);

  @override
  State<ReviewCard> createState() => _ReviewCardState();
}

class _ReviewCardState extends State<ReviewCard> {
  final ReviewService _reviewService = ReviewService();
  bool _isVoting = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with user info and rating
            Row(
              children: [
                // User avatar
                CircleAvatar(
                  radius: 20,
                  backgroundColor: AppColor.primary.withOpacity(0.1),
                  child: widget.review.userProfileImage.isNotEmpty
                      ? CustomImage(
                          widget.review.userProfileImage,
                          width: 40,
                          height: 40,
                          radius: 20,
                        )
                      : Icon(
                          Icons.person,
                          color: AppColor.primary,
                          size: 20,
                        ),
                ),
                const SizedBox(width: 12),
                
                // User name and date
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            widget.review.userName,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: AppColor.textColor,
                              fontSize: 14,
                            ),
                          ),
                          if (widget.review.isVerified) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.verified,
                              color: Colors.blue,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      Text(
                        widget.review.timeAgo,
                        style: TextStyle(
                          color: AppColor.labelColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Rating
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getRatingColor(widget.review.rating).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.star,
                        color: _getRatingColor(widget.review.rating),
                        size: 14,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        widget.review.rating.toStringAsFixed(1),
                        style: TextStyle(
                          color: _getRatingColor(widget.review.rating),
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Review title
            if (widget.review.title.isNotEmpty) ...[
              Text(
                widget.review.title,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColor.textColor,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
            ],
            
            // Review comment
            Text(
              widget.review.comment,
              style: TextStyle(
                color: AppColor.textColor,
                fontSize: 14,
                height: 1.4,
              ),
            ),
            
            // Review images
            if (widget.review.images.isNotEmpty) ...[
              const SizedBox(height: 12),
              SizedBox(
                height: 80,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: widget.review.images.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: GestureDetector(
                        onTap: () => _showImageDialog(widget.review.images[index]),
                        child: CustomImage(
                          widget.review.images[index],
                          width: 80,
                          height: 80,
                          radius: 8,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
            
            const SizedBox(height: 12),
            
            // Actions row
            Row(
              children: [
                // Helpful votes
                if (widget.canInteract) ...[
                  _buildVoteButton(
                    icon: Icons.thumb_up_outlined,
                    count: widget.review.helpfulCount,
                    isSelected: false, // TODO: Check if current user voted
                    onTap: () => _voteOnReview(true),
                  ),
                  const SizedBox(width: 16),
                  _buildVoteButton(
                    icon: Icons.thumb_down_outlined,
                    count: widget.review.notHelpfulCount,
                    isSelected: false, // TODO: Check if current user voted
                    onTap: () => _voteOnReview(false),
                  ),
                ],
                
                const Spacer(),
                
                // Edit/Delete buttons for own reviews
                if (widget.onEdit != null) ...[
                  IconButton(
                    icon: Icon(Icons.edit, color: AppColor.primary, size: 20),
                    onPressed: widget.onEdit,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
                if (widget.onDelete != null) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: Icon(Icons.delete, color: Colors.red, size: 20),
                    onPressed: widget.onDelete,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoteButton({
    required IconData icon,
    required int count,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: _isVoting ? null : onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: isSelected ? AppColor.primary : AppColor.labelColor,
          ),
          const SizedBox(width: 4),
          Text(
            count.toString(),
            style: TextStyle(
              color: isSelected ? AppColor.primary : AppColor.labelColor,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Color _getRatingColor(double rating) {
    if (rating >= 4.0) return Colors.green;
    if (rating >= 3.0) return Colors.orange;
    return Colors.red;
  }

  Future<void> _voteOnReview(bool isHelpful) async {
    if (_isVoting) return;
    
    setState(() {
      _isVoting = true;
    });

    try {
      await _reviewService.voteOnReview(widget.review.id, isHelpful);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Thank you for your feedback!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isVoting = false;
        });
      }
    }
  }

  void _showImageDialog(String imageUrl) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: Icon(Icons.close, color: AppColor.textColor),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
              Expanded(
                child: CustomImage(
                  imageUrl,
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.contain,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
