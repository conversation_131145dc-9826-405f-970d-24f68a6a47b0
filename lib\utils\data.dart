var profile = {
  "name": "Tamil S",
  "image": "https://avatars.githubusercontent.com/u/86506519?v=4", // replace with your image if needed
  "email": "<EMAIL>",
  "phone": "+91 6379869678 "
};

List categories = [
  {"name": "All", "icon": "assets/icons/home.svg"},
  {"name": "Single Room", "icon": "assets/icons/home.svg"},
  {"name": "Double Room", "icon": "assets/icons/home.svg"},
  {"name": "Family Room", "icon": "assets/icons/home.svg"},
  {"name": "Queen Room", "icon": "assets/icons/home.svg"},
  {"name": "King Room", "icon": "assets/icons/home.svg"},
  {"name": "Bungalow", "icon": "assets/icons/home.svg"},
  {"name": "Single Villa", "icon": "assets/icons/home.svg"},
  {"name": "Apartment", "icon": "assets/icons/home.svg"},
];

List cities = [
  {"name": "Tiruvannamalai", "icon": "assets/icons/home.svg"},
];

List<String> albumImages = [
  "https://images.unsplash.com/photo-1598928636135-d146006ff4be?auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1505692952047-1a78307da8f2?auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1618221118493-9cfa1a1c00da?auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1571508601891-ca5e7a713859?auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1611892440504-42a792e24d32?auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1596394516093-501ba68a0ba6?auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1589987601054-e05dd8d1fae0?auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1554995207-c18c203602cb?auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1560067174-894dc1e13d2b?auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1600585152909-dec4d9e95a06?auto=format&fit=crop&w=800&q=60",
];

List features = [
  {
    "id": 200,
    "name": "Single Room Standard",
    "image": albumImages[0],
    "price": "₹1000",
    "type": "Single Room",
    "rate": "4.2",
    "location": "Tiruvannamalai",
    "is_favorited": false,
    "album_images": albumImages,
    "description": "A comfortable single room near Arunachaleswarar Temple."
  },
  {
    "id": 201,
    "name": "Double Room Deluxe",
    "image": albumImages[1],
    "price": "₹1800",
    "type": "Double Room",
    "rate": "4.4",
    "location": "Tiruvannamalai",
    "is_favorited": false,
    "album_images": albumImages,
    "description": "Spacious double room for family or couples."
  },
  {
    "id": 202,
    "name": "Family Room Large",
    "image": albumImages[2],
    "price": "₹2500",
    "type": "Family Room",
    "rate": "4.6",
    "location": "Tiruvannamalai",
    "is_favorited": true,
    "album_images": albumImages,
    "description": "Perfect for family stay with clean surroundings."
  },
  {
    "id": 203,
    "name": "King Room Premium",
    "image": albumImages[3],
    "price": "₹3200",
    "type": "King Room",
    "rate": "4.8",
    "location": "Tiruvannamalai",
    "is_favorited": false,
    "album_images": albumImages,
    "description": "Luxury king room with AC and hill view."
  },
];

List recommends = [
  {
    "id": 204,
    "name": "Queen Room Comfort",
    "image": albumImages[4],
    "price": "₹2700",
    "type": "Queen Room",
    "rate": "4.7",
    "location": "Tiruvannamalai",
    "is_favorited": false,
    "album_images": albumImages,
    "description": "Well-furnished queen bed room with peaceful atmosphere."
  },
  {
    "id": 205,
    "name": "Bungalow Stay",
    "image": albumImages[5],
    "price": "₹3500",
    "type": "Bungalow",
    "rate": "4.9",
    "location": "Tiruvannamalai",
    "is_favorited": true,
    "album_images": albumImages,
    "description": "Private bungalow for long stays near Girivalam Path."
  },
  {
    "id": 206,
    "name": "Single Villa",
    "image": albumImages[6],
    "price": "₹2900",
    "type": "Single Villa",
    "rate": "4.6",
    "location": "Tiruvannamalai",
    "is_favorited": false,
    "album_images": albumImages,
    "description": "Private villa with garden view and amenities."
  },
];
