import '../utils/currency_formatter.dart';

class TiruvannamalaiData {
  // Enhanced Tiruvannamalai Hotels with Surrounding Areas
  static List<Map<String, dynamic>> tiruvannamalaiHotels = [
    {
      'name': 'Ramana Maharshi Ashram Resort',
      'description': 'Spiritual retreat resort in Tiruvannamalai with serene ambiance, mountain views, and proximity to Arunachala Temple. Experience divine tranquility.',
      'address': 'Ramana Nagar, Tiruvannamalai, Tamil Nadu 606603',
      'city': 'Tiruvannamalai',
      'country': 'India',
      'latitude': 12.2253,
      'longitude': 79.0747,
      'images': [
        'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80', // Mountain resort
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80', // Luxury hotel exterior
        'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&q=80', // Hotel lobby
        'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80', // Room interior
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80', // Hotel pool
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80', // Spa area
      ],
      'rating': 4.4,
      'reviewCount': 289,
      'amenities': ['WiFi', 'Restaurant', 'Temple Tours', 'Meditation Hall', 'Garden', 'Parking', 'Spiritual Library', 'Yoga Classes', 'Ayurveda Spa'],
      'pricePerNight': 6500.0,
      'currency': 'INR',
      'isAvailable': true,
      'specialFeatures': ['Arunachala Mountain View', 'Temple Proximity', 'Spiritual Atmosphere', 'Meditation Programs'],
      'nearbyAttractions': ['Arunachaleswarar Temple', 'Ramana Ashram', 'Virupaksha Cave', 'Skandashram'],
    },
    {
      'name': 'Arunachala Heritage Hotel',
      'description': 'Traditional Tamil architecture hotel offering authentic cultural experience with modern amenities in the heart of Tiruvannamalai.',
      'address': 'Car Street, Near Arunachaleswarar Temple, Tiruvannamalai 606601',
      'city': 'Tiruvannamalai',
      'country': 'India',
      'latitude': 12.2308,
      'longitude': 79.0717,
      'images': [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80', // Heritage building
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80', // Traditional courtyard
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80', // Heritage room
        'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80', // Temple view
        'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80', // Traditional dining
      ],
      'rating': 4.2,
      'reviewCount': 156,
      'amenities': ['WiFi', 'Traditional Restaurant', 'Cultural Programs', 'Temple Tours', 'Heritage Architecture', 'Parking'],
      'pricePerNight': 4500.0,
      'currency': 'INR',
      'isAvailable': true,
      'specialFeatures': ['Heritage Building', 'Temple Walking Distance', 'Cultural Experience', 'Traditional Cuisine'],
      'nearbyAttractions': ['Arunachaleswarar Temple', 'Local Markets', 'Heritage Streets', 'Traditional Shops'],
    },
    {
      'name': 'Spiritual Valley Resort',
      'description': 'Eco-friendly resort nestled in the foothills of Arunachala with organic farming, nature trails, and spiritual wellness programs.',
      'address': 'Chengam Road, Tiruvannamalai, Tamil Nadu 606604',
      'city': 'Tiruvannamalai',
      'country': 'India',
      'latitude': 12.2156,
      'longitude': 79.0892,
      'images': [
        'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80', // Valley resort
        'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80', // Eco cottage
        'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&q=80', // Nature view
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80', // Organic garden
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80', // Wellness center
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80', // Natural pool
      ],
      'rating': 4.6,
      'reviewCount': 234,
      'amenities': ['WiFi', 'Organic Restaurant', 'Nature Trails', 'Wellness Center', 'Organic Farm', 'Yoga Pavilion', 'Natural Pool'],
      'pricePerNight': 7500.0,
      'currency': 'INR',
      'isAvailable': true,
      'specialFeatures': ['Eco-Friendly', 'Organic Farm', 'Nature Immersion', 'Wellness Programs'],
      'nearbyAttractions': ['Arunachala Hills', 'Nature Trails', 'Organic Farms', 'Bird Watching Areas'],
    },
    {
      'name': 'Temple View Boutique Hotel',
      'description': 'Modern boutique hotel with panoramic temple views, rooftop dining, and contemporary amenities while maintaining spiritual essence.',
      'address': 'Girivalam Road, Tiruvannamalai, Tamil Nadu 606601',
      'city': 'Tiruvannamalai',
      'country': 'India',
      'latitude': 12.2289,
      'longitude': 79.0698,
      'images': [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80', // Boutique hotel
        'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80', // Modern room
        'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80', // Rooftop dining
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80', // Temple view
        'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80', // Modern amenities
      ],
      'rating': 4.3,
      'reviewCount': 187,
      'amenities': ['WiFi', 'Rooftop Restaurant', 'Temple Views', 'Modern Amenities', 'Concierge Service', 'Parking'],
      'pricePerNight': 5500.0,
      'currency': 'INR',
      'isAvailable': true,
      'specialFeatures': ['Temple Views', 'Rooftop Dining', 'Modern Design', 'Central Location'],
      'nearbyAttractions': ['Arunachaleswarar Temple', 'Girivalam Path', 'Local Markets', 'Cultural Centers'],
    },
  ];

  // Enhanced Room Data for Tiruvannamalai Hotels
  static Map<String, List<Map<String, dynamic>>> tiruvannamalaiRooms = {
    'Ramana Maharshi Ashram Resort': [
      {
        'name': 'Spiritual Retreat Room',
        'description': 'Peaceful room with Arunachala mountain view, meditation corner, spiritual books, and serene ambiance perfect for inner reflection.',
        'type': 'Spiritual Retreat',
        'images': [
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80', // Peaceful room
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80', // Meditation corner
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80', // Mountain view
          'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80', // Spiritual ambiance
        ],
        'pricePerNight': 6500.0,
        'currency': 'INR',
        'maxOccupancy': 2,
        'amenities': ['WiFi', 'Air Conditioning', 'Meditation Corner', 'Mountain View', 'Spiritual Books', 'Prayer Mat', 'Incense'],
        'isAvailable': true,
        'availableRooms': 8,
        'size': 30.0,
        'bedType': 'Twin Beds',
        'hasBalcony': true,
        'hasKitchen': false,
        'view': 'Arunachala Mountain View',
        'specialFeatures': ['Meditation Space', 'Spiritual Library', 'Mountain Views', 'Peaceful Environment'],
      },
      {
        'name': 'Ashram Deluxe Suite',
        'description': 'Spacious suite with separate meditation area, temple view, premium spiritual amenities, and traditional Tamil decor.',
        'type': 'Deluxe Suite',
        'images': [
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80', // Deluxe suite
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80', // Meditation area
          'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80', // Temple view
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80', // Traditional decor
        ],
        'pricePerNight': 9500.0,
        'currency': 'INR',
        'maxOccupancy': 3,
        'amenities': ['WiFi', 'Air Conditioning', 'Meditation Area', 'Temple View', 'Spiritual Library', 'Balcony', 'Mini Fridge'],
        'isAvailable': true,
        'availableRooms': 4,
        'size': 45.0,
        'bedType': 'King Bed + Single Bed',
        'hasBalcony': true,
        'hasKitchen': false,
        'view': 'Arunachaleswarar Temple View',
        'specialFeatures': ['Separate Meditation Room', 'Temple Views', 'Premium Amenities', 'Traditional Design'],
      },
      {
        'name': 'Family Spiritual Cottage',
        'description': 'Traditional cottage-style accommodation perfect for families seeking spiritual retreat with garden views and family meditation space.',
        'type': 'Family Cottage',
        'images': [
          'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80', // Family cottage
          'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80', // Garden view
          'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&q=80', // Family space
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80', // Cottage interior
        ],
        'pricePerNight': 8000.0,
        'currency': 'INR',
        'maxOccupancy': 4,
        'amenities': ['WiFi', 'Air Conditioning', 'Kitchenette', 'Garden View', 'Family Meditation Space', 'Parking', 'Children Play Area'],
        'isAvailable': true,
        'availableRooms': 6,
        'size': 55.0,
        'bedType': '2 Double Beds',
        'hasBalcony': false,
        'hasKitchen': true,
        'view': 'Garden & Mountain View',
        'specialFeatures': ['Family Friendly', 'Garden Access', 'Kitchenette', 'Children Facilities'],
      },
    ],
    'Arunachala Heritage Hotel': [
      {
        'name': 'Heritage Classic Room',
        'description': 'Traditional Tamil architecture room with antique furniture, cultural artifacts, and authentic heritage experience.',
        'type': 'Heritage Classic',
        'images': [
          'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80', // Heritage room
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80', // Traditional decor
          'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80', // Antique furniture
          'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80', // Cultural artifacts
        ],
        'pricePerNight': 4500.0,
        'currency': 'INR',
        'maxOccupancy': 2,
        'amenities': ['WiFi', 'Air Conditioning', 'Heritage Decor', 'Cultural Artifacts', 'Traditional Furniture', 'Courtyard View'],
        'isAvailable': true,
        'availableRooms': 10,
        'size': 28.0,
        'bedType': 'Queen Bed',
        'hasBalcony': false,
        'hasKitchen': false,
        'view': 'Heritage Courtyard',
        'specialFeatures': ['Authentic Heritage', 'Cultural Experience', 'Traditional Design', 'Historical Ambiance'],
      },
      {
        'name': 'Temple View Heritage Suite',
        'description': 'Premium heritage suite with direct temple views, traditional Tamil architecture, and luxury amenities in historic setting.',
        'type': 'Heritage Suite',
        'images': [
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80', // Heritage suite
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80', // Temple view
          'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80', // Traditional architecture
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80', // Luxury amenities
        ],
        'pricePerNight': 6500.0,
        'currency': 'INR',
        'maxOccupancy': 3,
        'amenities': ['WiFi', 'Air Conditioning', 'Temple View', 'Heritage Architecture', 'Premium Amenities', 'Balcony', 'Cultural Programs'],
        'isAvailable': true,
        'availableRooms': 3,
        'size': 40.0,
        'bedType': 'King Bed + Daybed',
        'hasBalcony': true,
        'hasKitchen': false,
        'view': 'Arunachaleswarar Temple',
        'specialFeatures': ['Direct Temple Views', 'Heritage Architecture', 'Cultural Programs', 'Premium Location'],
      },
    ],
    'Spiritual Valley Resort': [
      {
        'name': 'Eco Valley Cottage',
        'description': 'Sustainable eco-cottage with organic materials, nature views, and eco-friendly amenities in peaceful valley setting.',
        'type': 'Eco Cottage',
        'images': [
          'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80', // Eco cottage
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80', // Nature view
          'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&q=80', // Organic materials
          'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80', // Valley setting
        ],
        'pricePerNight': 7500.0,
        'currency': 'INR',
        'maxOccupancy': 2,
        'amenities': ['WiFi', 'Eco-Friendly', 'Nature View', 'Organic Materials', 'Solar Power', 'Rainwater Harvesting', 'Organic Garden Access'],
        'isAvailable': true,
        'availableRooms': 8,
        'size': 35.0,
        'bedType': 'Eco Bed',
        'hasBalcony': true,
        'hasKitchen': false,
        'view': 'Valley & Hills',
        'specialFeatures': ['Eco-Friendly', 'Sustainable Living', 'Nature Immersion', 'Organic Experience'],
      },
      {
        'name': 'Wellness Retreat Villa',
        'description': 'Luxury villa with private wellness facilities, yoga deck, organic garden access, and comprehensive wellness programs.',
        'type': 'Wellness Villa',
        'images': [
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80', // Wellness villa
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80', // Yoga deck
          'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80', // Wellness facilities
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80', // Organic garden
        ],
        'pricePerNight': 12000.0,
        'currency': 'INR',
        'maxOccupancy': 4,
        'amenities': ['WiFi', 'Private Yoga Deck', 'Wellness Facilities', 'Organic Garden', 'Spa Services', 'Meditation Space', 'Healthy Cuisine'],
        'isAvailable': true,
        'availableRooms': 4,
        'size': 65.0,
        'bedType': 'King Bed + Twin Beds',
        'hasBalcony': true,
        'hasKitchen': true,
        'view': 'Panoramic Valley View',
        'specialFeatures': ['Private Wellness Center', 'Yoga Programs', 'Spa Services', 'Organic Living'],
      },
    ],
    'Temple View Boutique Hotel': [
      {
        'name': 'Modern Temple View Room',
        'description': 'Contemporary room with panoramic temple views, modern amenities, and stylish design while maintaining spiritual connection.',
        'type': 'Modern Deluxe',
        'images': [
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80', // Modern room
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80', // Temple view
          'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80', // Stylish design
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80', // Modern amenities
        ],
        'pricePerNight': 5500.0,
        'currency': 'INR',
        'maxOccupancy': 2,
        'amenities': ['WiFi', 'Air Conditioning', 'Temple View', 'Modern Design', 'Smart TV', 'Mini Bar', 'Work Desk'],
        'isAvailable': true,
        'availableRooms': 12,
        'size': 32.0,
        'bedType': 'King Bed',
        'hasBalcony': true,
        'hasKitchen': false,
        'view': 'Arunachaleswarar Temple',
        'specialFeatures': ['Panoramic Temple Views', 'Modern Design', 'Smart Amenities', 'Central Location'],
      },
      {
        'name': 'Rooftop Penthouse Suite',
        'description': 'Luxury penthouse with 360-degree views, private rooftop access, premium amenities, and exclusive dining options.',
        'type': 'Penthouse Suite',
        'images': [
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80', // Penthouse suite
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80', // Rooftop access
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80', // 360 views
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80', // Premium amenities
        ],
        'pricePerNight': 9500.0,
        'currency': 'INR',
        'maxOccupancy': 4,
        'amenities': ['WiFi', 'Private Rooftop', '360° Views', 'Premium Amenities', 'Exclusive Dining', 'Jacuzzi', 'Butler Service'],
        'isAvailable': true,
        'availableRooms': 2,
        'size': 75.0,
        'bedType': 'King Bed + Sofa Bed',
        'hasBalcony': true,
        'hasKitchen': true,
        'view': '360° City & Temple View',
        'specialFeatures': ['Private Rooftop', '360° Views', 'Exclusive Access', 'Luxury Amenities'],
      },
    ],
  };

  // Get all Tiruvannamalai hotels
  static List<Map<String, dynamic>> getAllHotels() {
    return tiruvannamalaiHotels;
  }

  // Get rooms for a specific hotel
  static List<Map<String, dynamic>> getRoomsForHotel(String hotelName) {
    return tiruvannamalaiRooms[hotelName] ?? [];
  }

  // Get hotel by name
  static Map<String, dynamic>? getHotelByName(String hotelName) {
    try {
      return tiruvannamalaiHotels.firstWhere((hotel) => hotel['name'] == hotelName);
    } catch (e) {
      return null;
    }
  }

  // Get formatted price for hotel
  static String getFormattedPrice(String hotelName) {
    final hotel = getHotelByName(hotelName);
    if (hotel != null) {
      return CurrencyFormatter.formatPerNight(hotel['pricePerNight']);
    }
    return '';
  }

  // Get all unique amenities
  static List<String> getAllAmenities() {
    Set<String> amenities = {};
    for (var hotel in tiruvannamalaiHotels) {
      amenities.addAll(List<String>.from(hotel['amenities']));
    }
    return amenities.toList()..sort();
  }

  // Get hotels by price range
  static List<Map<String, dynamic>> getHotelsByPriceRange(double minPrice, double maxPrice) {
    return tiruvannamalaiHotels.where((hotel) {
      double price = hotel['pricePerNight'];
      return price >= minPrice && price <= maxPrice;
    }).toList();
  }

  // Get hotels by rating
  static List<Map<String, dynamic>> getHotelsByRating(double minRating) {
    return tiruvannamalaiHotels.where((hotel) {
      double rating = hotel['rating'];
      return rating >= minRating;
    }).toList();
  }

  // Tiruvannamalai Attractions and Features
  static Map<String, dynamic> tiruvannamalaiInfo = {
    'city': 'Tiruvannamalai',
    'state': 'Tamil Nadu',
    'description': 'Sacred city known for Arunachaleswarar Temple and spiritual significance',
    'mainAttractions': [
      {
        'name': 'Arunachaleswarar Temple',
        'description': 'Ancient Shiva temple, one of the Pancha Bhoota Stalas',
        'distance': '0.5 km',
        'image': 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80',
      },
      {
        'name': 'Ramana Ashram',
        'description': 'Spiritual ashram of Sri Ramana Maharshi',
        'distance': '1.2 km',
        'image': 'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80',
      },
      {
        'name': 'Virupaksha Cave',
        'description': 'Sacred cave where Ramana Maharshi meditated',
        'distance': '2.5 km',
        'image': 'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&q=80',
      },
      {
        'name': 'Skandashram',
        'description': 'Ashram on Arunachala Hill with panoramic views',
        'distance': '3.0 km',
        'image': 'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80',
      },
    ],
    'spiritualExperiences': [
      'Girivalam (circumambulation of Arunachala)',
      'Temple darshan and prayers',
      'Meditation and yoga sessions',
      'Spiritual discourses and satsangs',
      'Sacred hill trekking',
    ],
    'localCuisine': [
      'Traditional Tamil vegetarian meals',
      'Temple prasadam',
      'Local street food',
      'Organic farm-to-table dining',
      'Ayurvedic healthy cuisine',
    ],
    'bestTimeToVisit': 'October to March',
    'climate': 'Tropical with moderate temperatures',
    'languages': ['Tamil', 'English', 'Hindi'],
    'transportation': [
      'Chennai Airport (195 km)',
      'Tiruvannamalai Railway Station',
      'Local buses and auto-rickshaws',
      'Taxi and cab services',
    ],
  };

  // Get Tiruvannamalai city information
  static Map<String, dynamic> getCityInfo() {
    return tiruvannamalaiInfo;
  }

  // Get main attractions
  static List<Map<String, dynamic>> getMainAttractions() {
    return List<Map<String, dynamic>>.from(tiruvannamalaiInfo['mainAttractions']);
  }

  // Get spiritual experiences
  static List<String> getSpiritualExperiences() {
    return List<String>.from(tiruvannamalaiInfo['spiritualExperiences']);
  }

  // Get local cuisine options
  static List<String> getLocalCuisine() {
    return List<String>.from(tiruvannamalaiInfo['localCuisine']);
  }

  // Search hotels by amenity
  static List<Map<String, dynamic>> searchHotelsByAmenity(String amenity) {
    return tiruvannamalaiHotels.where((hotel) {
      List<String> amenities = List<String>.from(hotel['amenities']);
      return amenities.any((a) => a.toLowerCase().contains(amenity.toLowerCase()));
    }).toList();
  }

  // Get hotel recommendations based on preferences
  static List<Map<String, dynamic>> getRecommendations({
    String? preference, // 'spiritual', 'luxury', 'eco', 'heritage'
    double? maxPrice,
    double? minRating,
  }) {
    List<Map<String, dynamic>> filtered = tiruvannamalaiHotels;

    if (preference != null) {
      switch (preference.toLowerCase()) {
        case 'spiritual':
          filtered = filtered.where((hotel) =>
            hotel['name'].toString().toLowerCase().contains('ashram') ||
            hotel['name'].toString().toLowerCase().contains('spiritual') ||
            hotel['name'].toString().toLowerCase().contains('ramana')
          ).toList();
          break;
        case 'luxury':
          filtered = filtered.where((hotel) => hotel['pricePerNight'] > 8000).toList();
          break;
        case 'eco':
          filtered = filtered.where((hotel) =>
            hotel['name'].toString().toLowerCase().contains('valley') ||
            hotel['name'].toString().toLowerCase().contains('eco')
          ).toList();
          break;
        case 'heritage':
          filtered = filtered.where((hotel) =>
            hotel['name'].toString().toLowerCase().contains('heritage')
          ).toList();
          break;
      }
    }

    if (maxPrice != null) {
      filtered = filtered.where((hotel) => hotel['pricePerNight'] <= maxPrice).toList();
    }

    if (minRating != null) {
      filtered = filtered.where((hotel) => hotel['rating'] >= minRating).toList();
    }

    return filtered;
  }
}
