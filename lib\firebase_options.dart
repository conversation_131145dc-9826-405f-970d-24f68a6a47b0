// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyApznZepj4lfrWK-cANQOEbXGK4hcooxRw',
    appId: '1:360826011508:web:d866be04f20bf8f9eff621',
    messagingSenderId: '360826011508',
    projectId: 'one-touch-hotel',
    authDomain: 'one-touch-hotel.firebaseapp.com',
    storageBucket: 'one-touch-hotel.firebasestorage.app',
    databaseURL: 'https://one-touch-hotel-default-rtdb.asia-southeast1.firebasedatabase.app',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyApznZepj4lfrWK-cANQOEbXGK4hcooxRw',
    appId: '1:360826011508:android:d866be04f20bf8f9eff621',
    messagingSenderId: '360826011508',
    projectId: 'one-touch-hotel',
    storageBucket: 'one-touch-hotel.firebasestorage.app',
    databaseURL: 'https://one-touch-hotel-default-rtdb.asia-southeast1.firebasedatabase.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyApznZepj4lfrWK-cANQOEbXGK4hcooxRw',
    appId: '1:360826011508:ios:d866be04f20bf8f9eff621',
    messagingSenderId: '360826011508',
    projectId: 'one-touch-hotel',
    storageBucket: 'one-touch-hotel.firebasestorage.app',
    databaseURL: 'https://one-touch-hotel-default-rtdb.asia-southeast1.firebasedatabase.app',
    iosClientId: '360826011508-du7gtuivr16e1l99l7euh60i2unbrssv.apps.googleusercontent.com',
    iosBundleId: 'com.sangvaleap.hotel_booking',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyApznZepj4lfrWK-cANQOEbXGK4hcooxRw',
    appId: '1:360826011508:ios:d866be04f20bf8f9eff621',
    messagingSenderId: '360826011508',
    projectId: 'one-touch-hotel',
    storageBucket: 'one-touch-hotel.firebasestorage.app',
    databaseURL: 'https://one-touch-hotel-default-rtdb.asia-southeast1.firebasedatabase.app',
    iosClientId: '360826011508-du7gtuivr16e1l99l7euh60i2unbrssv.apps.googleusercontent.com',
    iosBundleId: 'com.sangvaleap.hotel_booking',
  );
}