import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:io';

class MemoryManager {
  static Timer? _memoryCheckTimer;
  static int _imagesCached = 0;
  static const int _maxCachedImages = 100;
  static const int _lowMemoryThreshold = 50; // MB
  
  // Initialize memory management
  static void initialize() {
    // Set image cache limits based on device capabilities
    _setImageCacheLimits();
    
    // Start periodic memory monitoring
    _startMemoryMonitoring();
    
    // Set up app lifecycle listener
    _setupAppLifecycleListener();
  }

  // Set appropriate image cache limits
  static void _setImageCacheLimits() {
    final imageCache = PaintingBinding.instance.imageCache;
    
    // Adaptive cache size based on platform
    if (Platform.isAndroid) {
      // Conservative limits for Android devices
      imageCache.maximumSize = _maxCachedImages;
      imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50MB
    } else {
      // More generous limits for other platforms
      imageCache.maximumSize = 200;
      imageCache.maximumSizeBytes = 100 * 1024 * 1024; // 100MB
    }
  }

  // Start monitoring memory usage
  static void _startMemoryMonitoring() {
    _memoryCheckTimer = Timer.periodic(
      const Duration(minutes: 2),
      (timer) => _checkMemoryUsage(),
    );
  }

  // Check memory usage and clean up if needed
  static void _checkMemoryUsage() {
    final imageCache = PaintingBinding.instance.imageCache;
    
    // Check if we're approaching memory limits
    if (imageCache.currentSize > _maxCachedImages * 0.8) {
      _performMemoryCleanup();
    }
  }

  // Perform memory cleanup
  static void _performMemoryCleanup() {
    final imageCache = PaintingBinding.instance.imageCache;
    
    // Clear half of the image cache
    imageCache.clear();
    imageCache.clearLiveImages();
    
    // Force garbage collection hint
    _requestGarbageCollection();
    
    debugPrint('Memory cleanup performed - Image cache cleared');
  }

  // Request garbage collection (hint to system)
  static void _requestGarbageCollection() {
    // This is just a hint to the system
    try {
      SystemChannels.platform.invokeMethod('System.gc');
    } catch (e) {
      // Ignore errors - this is just a hint
    }
  }

  // Set up app lifecycle listener
  static void _setupAppLifecycleListener() {
    SystemChannels.lifecycle.setMessageHandler((message) async {
      switch (message) {
        case 'AppLifecycleState.paused':
          _onAppPaused();
          break;
        case 'AppLifecycleState.resumed':
          _onAppResumed();
          break;
        case 'AppLifecycleState.detached':
          _onAppDetached();
          break;
      }
      return null;
    });
  }

  // Handle app paused state
  static void _onAppPaused() {
    // Aggressive cleanup when app goes to background
    _performMemoryCleanup();
    
    // Pause memory monitoring
    _memoryCheckTimer?.cancel();
    
    debugPrint('App paused - Memory cleanup performed');
  }

  // Handle app resumed state
  static void _onAppResumed() {
    // Resume memory monitoring
    _startMemoryMonitoring();
    
    debugPrint('App resumed - Memory monitoring restarted');
  }

  // Handle app detached state
  static void _onAppDetached() {
    // Final cleanup
    dispose();
  }

  // Dispose resources
  static void dispose() {
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = null;
    
    // Final memory cleanup
    _performMemoryCleanup();
  }

  // Manual memory cleanup (can be called by app)
  static void clearCache() {
    _performMemoryCleanup();
  }

  // Get current memory usage info
  static Map<String, dynamic> getMemoryInfo() {
    final imageCache = PaintingBinding.instance.imageCache;
    
    return {
      'cachedImages': imageCache.currentSize,
      'maxImages': imageCache.maximumSize,
      'cacheBytes': imageCache.currentSizeBytes,
      'maxBytes': imageCache.maximumSizeBytes,
      'memoryPressure': _getMemoryPressureLevel(),
    };
  }

  // Get memory pressure level
  static String _getMemoryPressureLevel() {
    final imageCache = PaintingBinding.instance.imageCache;
    final usage = imageCache.currentSize / imageCache.maximumSize;
    
    if (usage > 0.8) return 'HIGH';
    if (usage > 0.6) return 'MEDIUM';
    return 'LOW';
  }

  // Preload critical images with memory management
  static Future<void> preloadImage(
    ImageProvider imageProvider,
    BuildContext context, {
    bool highPriority = false,
  }) async {
    if (_imagesCached >= _maxCachedImages && !highPriority) {
      // Skip preloading if cache is full and not high priority
      return;
    }

    try {
      await precacheImage(imageProvider, context);
      _imagesCached++;
    } catch (e) {
      debugPrint('Failed to preload image: $e');
    }
  }

  // Optimized image widget with memory management
  static Widget buildMemoryOptimizedImage({
    required String imageUrl,
    required double width,
    required double height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: Image.network(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        // Optimize memory usage
        cacheWidth: width.toInt(),
        cacheHeight: height.toInt(),
        // Memory-efficient loading
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          
          return Container(
            width: width,
            height: height,
            color: Colors.grey[200],
            child: Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: width,
            height: height,
            color: Colors.grey[300],
            child: Icon(
              Icons.broken_image,
              color: Colors.grey[500],
              size: 24,
            ),
          );
        },
      ),
    );
  }

  // Check if device has low memory
  static bool isLowMemoryDevice() {
    final imageCache = PaintingBinding.instance.imageCache;
    return imageCache.maximumSizeBytes < 100 * 1024 * 1024; // Less than 100MB
  }

  // Adaptive list building with memory management
  static Widget buildMemoryOptimizedList({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    ScrollController? controller,
    EdgeInsets? padding,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      // Memory optimizations
      cacheExtent: isLowMemoryDevice() ? 200 : 500,
      addAutomaticKeepAlives: !isLowMemoryDevice(),
      addRepaintBoundaries: true,
    );
  }

  // Memory-efficient grid view
  static Widget buildMemoryOptimizedGrid({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    required int crossAxisCount,
    double childAspectRatio = 1.0,
    ScrollController? controller,
    EdgeInsets? padding,
  }) {
    return GridView.builder(
      controller: controller,
      padding: padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      // Memory optimizations
      cacheExtent: isLowMemoryDevice() ? 200 : 400,
      addAutomaticKeepAlives: !isLowMemoryDevice(),
    );
  }

  // Get recommended image quality based on memory
  static int getRecommendedImageQuality() {
    if (isLowMemoryDevice()) return 60;
    
    final memoryInfo = getMemoryInfo();
    final pressure = memoryInfo['memoryPressure'] as String;
    
    switch (pressure) {
      case 'HIGH':
        return 60;
      case 'MEDIUM':
        return 75;
      default:
        return 90;
    }
  }

  // Memory usage statistics for debugging
  static void printMemoryStats() {
    final info = getMemoryInfo();
    debugPrint('=== Memory Stats ===');
    debugPrint('Cached Images: ${info['cachedImages']}/${info['maxImages']}');
    debugPrint('Cache Bytes: ${(info['cacheBytes'] / 1024 / 1024).toStringAsFixed(1)}MB / ${(info['maxBytes'] / 1024 / 1024).toStringAsFixed(1)}MB');
    debugPrint('Memory Pressure: ${info['memoryPressure']}');
    debugPrint('==================');
  }
}
