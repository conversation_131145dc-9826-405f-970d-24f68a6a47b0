package com.sangvaleap.hotel_booking

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugins.GeneratedPluginRegistrant
import android.os.Bundle

class MainActivity: FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Ensure the activity is properly initialized
        try {
            // This helps with black screen issues
            window.statusBarColor = android.graphics.Color.TRANSPARENT
        } catch (e: Exception) {
            // Handle any initialization errors
            e.printStackTrace()
        }
    }
}
