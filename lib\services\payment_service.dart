import 'package:flutter/material.dart';
import '../models/booking.dart';
import 'booking_service.dart';
import 'email_service.dart';

class PaymentService {
  // Cashfree Test Credentials
  static const String _appId = 'TEST106652738300745b2d52508dde3c37256601';
  static const String _secretKey = 'cfsk_ma_test_a178392a1735b324d345e673262f3126_d57db2e8';
  static const String _environment = 'TEST'; // Use 'PROD' for production

  final BookingService _bookingService = BookingService();
  final EmailService _emailService = EmailService();

  // Initialize Cashfree (Simulated for demo)
  Future<void> initializeCashfree() async {
    try {
      // Simulate initialization
      await Future.delayed(const Duration(milliseconds: 500));
      print('Cashfree initialized successfully (Demo Mode)');
    } catch (e) {
      print('Error initializing Cashfree: $e');
      throw 'Failed to initialize payment gateway';
    }
  }

  // Create payment session
  Future<Map<String, dynamic>> createPaymentSession({
    required String orderId,
    required double amount,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
  }) async {
    try {
      // Create payment session data
      Map<String, dynamic> sessionData = {
        'payment_session_id': 'session_${orderId}_${DateTime.now().millisecondsSinceEpoch}',
        'order_id': orderId,
        'order_amount': amount,
        'order_currency': 'INR',
        'customer_details': {
          'customer_id': 'customer_${DateTime.now().millisecondsSinceEpoch}',
          'customer_name': customerName,
          'customer_email': customerEmail,
          'customer_phone': customerPhone,
        },
        'order_meta': {
          'return_url': 'https://test.cashfree.com/pgappsdks/return',
          'notify_url': 'https://test.cashfree.com/pgappsdks/notify',
        }
      };

      return sessionData;
    } catch (e) {
      print('Error creating payment session: $e');
      throw 'Failed to create payment session';
    }
  }

  // Process payment (Simulated for demo with Cashfree integration ready)
  Future<bool> processPayment({
    required BuildContext context,
    required String bookingId,
    required double amount,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
  }) async {
    try {
      // Initialize Cashfree
      await initializeCashfree();

      // Create order ID
      String orderId = 'ORDER_${bookingId}_${DateTime.now().millisecondsSinceEpoch}';

      // Show payment options dialog
      bool? paymentChoice = await _showPaymentOptionsDialog(context, amount);
      if (paymentChoice == null) return false;

      // Show payment processing
      bool paymentSuccess = await _processPaymentWithUI(context, orderId, amount);

      if (paymentSuccess) {
        // Update booking payment status
        await _bookingService.updatePaymentStatus(
          bookingId: bookingId,
          paymentStatus: PaymentStatus.paid,
          paymentId: orderId,
        );

        // Send confirmation email
        await _sendPaymentConfirmationEmail(
          bookingId: bookingId,
          customerEmail: customerEmail,
          customerName: customerName,
          amount: amount,
          orderId: orderId,
        );

        return true;
      } else {
        // Update booking payment status to failed
        await _bookingService.updatePaymentStatus(
          bookingId: bookingId,
          paymentStatus: PaymentStatus.failed,
        );

        return false;
      }
    } catch (e) {
      print('Error processing payment: $e');

      // Update booking payment status to failed
      await _bookingService.updatePaymentStatus(
        bookingId: bookingId,
        paymentStatus: PaymentStatus.failed,
      );

      throw 'Payment processing failed: $e';
    }
  }

  // Show payment options dialog
  Future<bool?> _showPaymentOptionsDialog(BuildContext context, double amount) async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Payment Method'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Amount to Pay: ₹${amount.toStringAsFixed(2)}',
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(Icons.credit_card),
                title: const Text('Credit/Debit Card'),
                onTap: () => Navigator.of(context).pop(true),
              ),
              ListTile(
                leading: const Icon(Icons.account_balance_wallet),
                title: const Text('UPI'),
                onTap: () => Navigator.of(context).pop(true),
              ),
              ListTile(
                leading: const Icon(Icons.account_balance),
                title: const Text('Net Banking'),
                onTap: () => Navigator.of(context).pop(true),
              ),
              ListTile(
                leading: const Icon(Icons.wallet),
                title: const Text('Digital Wallet'),
                onTap: () => Navigator.of(context).pop(true),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  // Process payment with UI (Simulated)
  Future<bool> _processPaymentWithUI(BuildContext context, String orderId, double amount) async {
    try {
      // Show payment processing dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Processing Payment'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text('Order ID: $orderId'),
                const SizedBox(height: 8),
                Text('Amount: ₹${amount.toStringAsFixed(2)}'),
                const SizedBox(height: 16),
                const Text('Please wait while we process your payment...'),
              ],
            ),
          );
        },
      );

      // Simulate payment processing time
      await Future.delayed(const Duration(seconds: 3));

      if (context.mounted) {
        Navigator.of(context).pop(); // Close processing dialog
      }

      // Simulate payment success (90% success rate for demo)
      bool paymentSuccess = DateTime.now().millisecond % 10 != 0;

      if (context.mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(paymentSuccess ? 'Payment Successful!' : 'Payment Failed'),
              content: Text(
                paymentSuccess
                    ? 'Your payment of ₹${amount.toStringAsFixed(2)} has been processed successfully.'
                    : 'Payment could not be processed. Please try again.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      }

      return paymentSuccess;
    } catch (e) {
      print('Error processing payment with UI: $e');
      return false;
    }
  }

  // Send payment confirmation email
  Future<void> _sendPaymentConfirmationEmail({
    required String bookingId,
    required String customerEmail,
    required String customerName,
    required double amount,
    required String orderId,
  }) async {
    try {
      // Get booking details
      Booking? booking = await _bookingService.getBookingById(bookingId);
      if (booking == null) return;

      // Send email notification
      await _emailService.sendPaymentConfirmation(
        toEmail: customerEmail,
        customerName: customerName,
        bookingId: bookingId,
        hotelName: booking.hotelName,
        roomName: booking.roomName,
        checkInDate: booking.checkInDate,
        checkOutDate: booking.checkOutDate,
        amount: amount,
        orderId: orderId,
      );
    } catch (e) {
      print('Error sending payment confirmation email: $e');
      // Don't throw error as payment was successful
    }
  }

  // Verify payment status (for webhook handling)
  Future<bool> verifyPaymentStatus(String orderId) async {
    try {
      // In a real implementation, you would call Cashfree's verify API
      // For demo purposes, we'll return true
      return true;
    } catch (e) {
      print('Error verifying payment status: $e');
      return false;
    }
  }

  // Handle payment failure
  Future<void> handlePaymentFailure({
    required String bookingId,
    required String customerEmail,
    required String customerName,
    String? failureReason,
  }) async {
    try {
      // Update booking status
      await _bookingService.updatePaymentStatus(
        bookingId: bookingId,
        paymentStatus: PaymentStatus.failed,
      );

      // Send failure notification email
      await _emailService.sendPaymentFailureNotification(
        toEmail: customerEmail,
        customerName: customerName,
        bookingId: bookingId,
        failureReason: failureReason ?? 'Payment processing failed',
      );
    } catch (e) {
      print('Error handling payment failure: $e');
    }
  }

  // Get supported payment methods
  List<String> getSupportedPaymentMethods() {
    return [
      'Credit/Debit Cards',
      'UPI',
      'Net Banking',
      'Digital Wallets',
      'EMI Options',
    ];
  }

  // Format amount for display
  String formatAmount(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  // Calculate processing fee (if any)
  double calculateProcessingFee(double amount) {
    // Example: 2% processing fee with minimum ₹10
    double fee = amount * 0.02;
    return fee < 10 ? 10 : fee;
  }
}
