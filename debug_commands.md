# Debug Commands for Black Screen Issue

## 1. Clean Build Process
```bash
flutter clean
flutter pub get
cd android
./gradlew clean
cd ..
```

## 2. Build Debug APK with Verbose Output
```bash
flutter build apk --debug --verbose
```

## 3. Install and Monitor Logs
```bash
flutter install
flutter logs
```

## 4. Alternative: Use Profile Mode
```bash
flutter build apk --profile
```

## 5. Check Device Logs
```bash
adb logcat | grep -E "(flutter|FirebaseApp|MainActivity)"
```

## 6. Test with Different Main Files

### Option A: Use Firebase Main (Current)
```bash
# Use lib/main.dart (current with Firebase)
flutter run --debug
```

### Option B: Use No-Firebase Main
```bash
# Temporarily rename files
mv lib/main.dart lib/main_firebase.dart
mv lib/main_no_firebase.dart lib/main.dart
flutter run --debug
```

### Option C: Use Simple Debug Main
```bash
# Use the newly created simple debug main
mv lib/main.dart lib/main_firebase.dart
mv lib/main_debug_simple.dart lib/main.dart
flutter run --debug
```

## 7. Check Firebase Configuration
If using Firebase main, ensure:
- google-services.json is in android/app/
- Firebase project is properly configured
- Internet connection is available

## 8. Gradle Issues Fix
If Gradle issues occur:
```bash
cd android
./gradlew --stop
./gradlew clean
cd ..
flutter clean
flutter pub get
```

## 9. Device-Specific Testing
```bash
# List connected devices
flutter devices

# Run on specific device
flutter run -d <device-id> --debug
```

## 10. Emergency Fallback
If all else fails, use the no-Firebase version:
```bash
cp lib/main_no_firebase.dart lib/main.dart
flutter build apk --debug
```

## Common Issues and Solutions

### Issue: Firebase Initialization Timeout
**Solution**: Use main_no_firebase.dart or main_debug_simple.dart

### Issue: Gradle Build Failure
**Solution**: Check android/gradle.properties and run gradle clean

### Issue: Plugin Registration Error
**Solution**: MainActivity.kt has been updated with proper plugin registration

### Issue: Manifest Merge Conflicts
**Solution**: AndroidManifest.xml has been updated with proper placeholders

### Issue: Memory Issues
**Solution**: gradle.properties has been updated with increased heap size
