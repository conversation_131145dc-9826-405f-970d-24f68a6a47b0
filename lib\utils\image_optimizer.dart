import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';

class ImageOptimizer {
  // Optimize image for different use cases
  static Future<Uint8List?> optimizeImage({
    required XFile imageFile,
    int maxWidth = 1024,
    int maxHeight = 1024,
    int quality = 80,
    ImageOptimizationType type = ImageOptimizationType.general,
  }) async {
    try {
      final Uint8List imageData = await imageFile.readAsBytes();
      
      // Decode the image
      final ui.Codec codec = await ui.instantiateImageCodec(
        imageData,
        targetWidth: maxWidth,
        targetHeight: maxHeight,
      );
      
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;
      
      // Convert to bytes with compression
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );
      
      if (byteData == null) return null;
      
      return byteData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error optimizing image: $e');
      return null;
    }
  }

  // Get optimized dimensions based on use case
  static Map<String, int> getOptimizedDimensions(ImageOptimizationType type) {
    switch (type) {
      case ImageOptimizationType.profile:
        return {'width': 512, 'height': 512};
      case ImageOptimizationType.thumbnail:
        return {'width': 300, 'height': 300};
      case ImageOptimizationType.gallery:
        return {'width': 1200, 'height': 800};
      case ImageOptimizationType.fullscreen:
        return {'width': 1920, 'height': 1080};
      case ImageOptimizationType.general:
      default:
        return {'width': 1024, 'height': 1024};
    }
  }

  // Get quality setting based on use case
  static int getOptimizedQuality(ImageOptimizationType type) {
    switch (type) {
      case ImageOptimizationType.profile:
        return 85;
      case ImageOptimizationType.thumbnail:
        return 70;
      case ImageOptimizationType.gallery:
        return 90;
      case ImageOptimizationType.fullscreen:
        return 95;
      case ImageOptimizationType.general:
      default:
        return 80;
    }
  }

  // Check if image needs optimization
  static Future<bool> needsOptimization({
    required XFile imageFile,
    int maxSizeKB = 500,
  }) async {
    try {
      final int sizeInBytes = await imageFile.length();
      final int sizeInKB = sizeInBytes ~/ 1024;
      return sizeInKB > maxSizeKB;
    } catch (e) {
      return false;
    }
  }

  // Get image dimensions
  static Future<Size?> getImageDimensions(XFile imageFile) async {
    try {
      final Uint8List imageData = await imageFile.readAsBytes();
      final ui.Codec codec = await ui.instantiateImageCodec(imageData);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;
      
      return Size(image.width.toDouble(), image.height.toDouble());
    } catch (e) {
      debugPrint('Error getting image dimensions: $e');
      return null;
    }
  }

  // Create thumbnail from image
  static Future<Uint8List?> createThumbnail({
    required XFile imageFile,
    int size = 150,
  }) async {
    return optimizeImage(
      imageFile: imageFile,
      maxWidth: size,
      maxHeight: size,
      quality: 70,
      type: ImageOptimizationType.thumbnail,
    );
  }

  // Validate image file
  static bool isValidImageFile(XFile file) {
    final String fileName = file.name.toLowerCase();
    const List<String> validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    
    return validExtensions.any((ext) => fileName.endsWith(ext));
  }

  // Get file size in different units
  static Future<Map<String, double>> getFileSizeInfo(XFile file) async {
    try {
      final int bytes = await file.length();
      return {
        'bytes': bytes.toDouble(),
        'kb': bytes / 1024,
        'mb': bytes / (1024 * 1024),
      };
    } catch (e) {
      return {'bytes': 0.0, 'kb': 0.0, 'mb': 0.0};
    }
  }

  // Check if file size is within limits
  static Future<bool> isFileSizeValid({
    required XFile file,
    double maxSizeMB = 5.0,
  }) async {
    final Map<String, double> sizeInfo = await getFileSizeInfo(file);
    return sizeInfo['mb']! <= maxSizeMB;
  }

  // Get memory-efficient cache dimensions
  static Map<String, int> getCacheDimensions({
    required double displayWidth,
    required double displayHeight,
    double pixelRatio = 1.0,
  }) {
    final int cacheWidth = (displayWidth * pixelRatio).round();
    final int cacheHeight = (displayHeight * pixelRatio).round();
    
    // Limit cache dimensions to prevent memory issues
    const int maxCacheDimension = 1024;
    
    return {
      'width': cacheWidth > maxCacheDimension ? maxCacheDimension : cacheWidth,
      'height': cacheHeight > maxCacheDimension ? maxCacheDimension : cacheHeight,
    };
  }

  // Generate placeholder image data
  static Future<Uint8List> generatePlaceholderImage({
    int width = 300,
    int height = 200,
    Color backgroundColor = Colors.grey,
    IconData icon = Icons.image,
    Color iconColor = Colors.white,
  }) async {
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    final Paint paint = Paint()..color = backgroundColor;
    
    // Draw background
    canvas.drawRect(Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()), paint);
    
    // Draw icon (simplified - in real implementation you'd need to draw the icon properly)
    final Paint iconPaint = Paint()..color = iconColor;
    final double iconSize = width * 0.3;
    final double iconX = (width - iconSize) / 2;
    final double iconY = (height - iconSize) / 2;
    
    canvas.drawCircle(
      Offset(iconX + iconSize / 2, iconY + iconSize / 2),
      iconSize / 2,
      iconPaint,
    );
    
    final ui.Picture picture = recorder.endRecording();
    final ui.Image image = await picture.toImage(width, height);
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }

  // Calculate optimal image loading strategy
  static ImageLoadingStrategy getOptimalLoadingStrategy({
    required double displayWidth,
    required double displayHeight,
    required String imageUrl,
    bool isListItem = false,
    bool isFullscreen = false,
  }) {
    if (isFullscreen) {
      return ImageLoadingStrategy.highQuality;
    } else if (isListItem) {
      return ImageLoadingStrategy.thumbnail;
    } else if (displayWidth > 500 || displayHeight > 500) {
      return ImageLoadingStrategy.mediumQuality;
    } else {
      return ImageLoadingStrategy.lowQuality;
    }
  }
}

enum ImageOptimizationType {
  profile,
  thumbnail,
  gallery,
  fullscreen,
  general,
}

enum ImageLoadingStrategy {
  thumbnail,
  lowQuality,
  mediumQuality,
  highQuality,
}

// Extension for easy image optimization
extension XFileOptimization on XFile {
  Future<Uint8List?> optimize({
    ImageOptimizationType type = ImageOptimizationType.general,
  }) async {
    final dimensions = ImageOptimizer.getOptimizedDimensions(type);
    final quality = ImageOptimizer.getOptimizedQuality(type);
    
    return ImageOptimizer.optimizeImage(
      imageFile: this,
      maxWidth: dimensions['width']!,
      maxHeight: dimensions['height']!,
      quality: quality,
      type: type,
    );
  }
  
  Future<bool> needsOptimization() async {
    return ImageOptimizer.needsOptimization(imageFile: this);
  }
  
  Future<Size?> getDimensions() async {
    return ImageOptimizer.getImageDimensions(this);
  }
  
  Future<Uint8List?> createThumbnail({int size = 150}) async {
    return ImageOptimizer.createThumbnail(imageFile: this, size: size);
  }
}
