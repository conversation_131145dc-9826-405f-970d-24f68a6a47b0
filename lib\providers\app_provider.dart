import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/hotel.dart';
import '../models/booking.dart';

class AppProvider extends ChangeNotifier {
  // Favorites
  List<String> _favoriteHotelIds = [];
  List<String> get favoriteHotelIds => _favoriteHotelIds;

  // Search history
  List<String> _searchHistory = [];
  List<String> get searchHistory => _searchHistory;

  // Recent bookings
  List<Booking> _recentBookings = [];
  List<Booking> get recentBookings => _recentBookings;

  // App settings
  bool _isDarkMode = false;
  bool get isDarkMode => _isDarkMode;

  String _preferredCurrency = 'INR';
  String get preferredCurrency => _preferredCurrency;

  // Loading states
  bool _isLoadingFavorites = false;
  bool get isLoadingFavorites => _isLoadingFavorites;

  AppProvider() {
    _loadPreferences();
  }

  // Initialize preferences
  Future<void> _loadPreferences() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      
      _favoriteHotelIds = prefs.getStringList('favorite_hotels') ?? [];
      _searchHistory = prefs.getStringList('search_history') ?? [];
      _isDarkMode = prefs.getBool('dark_mode') ?? false;
      _preferredCurrency = prefs.getString('preferred_currency') ?? 'INR';
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading preferences: $e');
    }
  }

  // Favorites management
  bool isFavorite(String hotelId) {
    return _favoriteHotelIds.contains(hotelId);
  }

  Future<void> toggleFavorite(String hotelId) async {
    try {
      if (_favoriteHotelIds.contains(hotelId)) {
        _favoriteHotelIds.remove(hotelId);
      } else {
        _favoriteHotelIds.add(hotelId);
      }
      
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('favorite_hotels', _favoriteHotelIds);
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error toggling favorite: $e');
    }
  }

  Future<void> addToFavorites(String hotelId) async {
    if (!_favoriteHotelIds.contains(hotelId)) {
      _favoriteHotelIds.add(hotelId);
      
      try {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setStringList('favorite_hotels', _favoriteHotelIds);
        notifyListeners();
      } catch (e) {
        debugPrint('Error adding to favorites: $e');
      }
    }
  }

  Future<void> removeFromFavorites(String hotelId) async {
    if (_favoriteHotelIds.contains(hotelId)) {
      _favoriteHotelIds.remove(hotelId);
      
      try {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setStringList('favorite_hotels', _favoriteHotelIds);
        notifyListeners();
      } catch (e) {
        debugPrint('Error removing from favorites: $e');
      }
    }
  }

  // Search history management
  Future<void> addToSearchHistory(String query) async {
    if (query.trim().isEmpty) return;
    
    // Remove if already exists to avoid duplicates
    _searchHistory.remove(query);
    
    // Add to beginning
    _searchHistory.insert(0, query);
    
    // Keep only last 10 searches
    if (_searchHistory.length > 10) {
      _searchHistory = _searchHistory.take(10).toList();
    }
    
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('search_history', _searchHistory);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding to search history: $e');
    }
  }

  Future<void> clearSearchHistory() async {
    _searchHistory.clear();
    
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('search_history', _searchHistory);
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing search history: $e');
    }
  }

  // App settings
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setBool('dark_mode', _isDarkMode);
      notifyListeners();
    } catch (e) {
      debugPrint('Error toggling dark mode: $e');
    }
  }

  Future<void> setPreferredCurrency(String currency) async {
    _preferredCurrency = currency;
    
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('preferred_currency', currency);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting preferred currency: $e');
    }
  }

  // Recent bookings
  void updateRecentBookings(List<Booking> bookings) {
    _recentBookings = bookings.take(5).toList(); // Keep only 5 most recent
    notifyListeners();
  }

  // Utility methods
  void setLoadingFavorites(bool loading) {
    _isLoadingFavorites = loading;
    notifyListeners();
  }

  // Clear all data (for logout)
  Future<void> clearAllData() async {
    _favoriteHotelIds.clear();
    _searchHistory.clear();
    _recentBookings.clear();
    _isDarkMode = false;
    _preferredCurrency = 'INR';
    
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing all data: $e');
    }
  }

  // Get favorite hotels count
  int get favoritesCount => _favoriteHotelIds.length;

  // Get recent searches (limited)
  List<String> get recentSearches => _searchHistory.take(5).toList();
}
